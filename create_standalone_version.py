#!/usr/bin/env python3
"""
Script to create a standalone executable version using PyInstaller
"""

import os
import subprocess
import sys
import shutil

def create_standalone_version():
    """Create standalone executable version"""
    
    print("🚀 إنشاء النسخة المستقلة (Standalone) لنظام إدارة الرواتب...")
    
    # Check if PyInstaller is installed
    try:
        import PyInstaller
        print("✅ PyInstaller موجود")
    except ImportError:
        print("📦 تثبيت PyInstaller...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ تم تثبيت PyInstaller")
    
    # Create spec file for PyInstaller
    create_pyinstaller_spec()
    
    # Build the executable
    build_executable()
    
    # Create portable package
    create_standalone_package()
    
    print("\n🎉 تم إنشاء النسخة المستقلة بنجاح!")

def create_pyinstaller_spec():
    """Create PyInstaller spec file"""
    print("📝 إنشاء ملف التكوين...")
    
    spec_content = """# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['simple_app.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('templates', 'templates'),
        ('static', 'static'),
    ],
    hiddenimports=[
        'flask',
        'flask_sqlalchemy',
        'werkzeug',
        'jinja2',
        'sqlite3',
        'datetime',
        'os',
        'hashlib',
        'functools'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='Qatar_Salary_System',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='static/favicon.ico' if os.path.exists('static/favicon.ico') else None,
)
"""
    
    with open("qatar_salary_system.spec", "w", encoding="utf-8") as f:
        f.write(spec_content)
    
    print("   ✅ تم إنشاء qatar_salary_system.spec")

def build_executable():
    """Build the executable using PyInstaller"""
    print("🔨 بناء الملف التنفيذي...")
    
    try:
        # Run PyInstaller
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm",
            "qatar_salary_system.spec"
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("   ✅ تم بناء الملف التنفيذي بنجاح")
        else:
            print(f"   ❌ خطأ في البناء: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ: {e}")
        return False
    
    return True

def create_standalone_package():
    """Create final standalone package"""
    print("📦 إنشاء الحزمة النهائية...")
    
    # Create standalone directory
    standalone_dir = "Qatar_Salary_System_Standalone"
    if os.path.exists(standalone_dir):
        shutil.rmtree(standalone_dir)
    os.makedirs(standalone_dir)
    
    # Copy executable
    exe_source = "dist/Qatar_Salary_System.exe" if os.name == 'nt' else "dist/Qatar_Salary_System"
    if os.path.exists(exe_source):
        shutil.copy2(exe_source, standalone_dir)
        print("   ✅ تم نسخ الملف التنفيذي")
    else:
        print("   ❌ الملف التنفيذي غير موجود")
        return
    
    # Create batch file for Windows
    if os.name == 'nt':
        create_windows_launcher(standalone_dir)
    
    # Create README
    create_standalone_readme(standalone_dir)
    
    # Create ZIP
    create_standalone_zip(standalone_dir)
    
    print(f"   ✅ تم إنشاء الحزمة في: {standalone_dir}")

def create_windows_launcher(standalone_dir):
    """Create Windows launcher"""
    launcher_content = """@echo off
echo ========================================
echo    نظام إدارة الرواتب القطري
echo    Qatar Salary Management System
echo ========================================
echo.
echo جاري تشغيل النظام...
echo Starting the system...
echo.
echo سيتم فتح النظام في المتصفح على العنوان:
echo The system will open in browser at:
echo http://localhost:7474
echo.
echo للإيقاف اضغط Ctrl+C
echo To stop press Ctrl+C
echo.

Qatar_Salary_System.exe

echo.
echo تم إيقاف النظام
echo System stopped
pause
"""
    
    with open(os.path.join(standalone_dir, "تشغيل_النظام.bat"), "w", encoding="utf-8") as f:
        f.write(launcher_content)
    
    with open(os.path.join(standalone_dir, "Run_System.bat"), "w", encoding="utf-8") as f:
        f.write(launcher_content)
    
    print("   ✅ تم إنشاء ملفات التشغيل")

def create_standalone_readme(standalone_dir):
    """Create README for standalone version"""
    readme_content = """# نظام إدارة الرواتب القطري - النسخة المستقلة
# Qatar Salary Management System - Standalone Version

## 🚀 التشغيل المباشر / Direct Run

### Windows:
- تشغيل `تشغيل_النظام.bat` أو `Run_System.bat`
- أو تشغيل `Qatar_Salary_System.exe` مباشرة

### Linux/Mac:
- تشغيل `./Qatar_Salary_System`

## ✨ مميزات النسخة المستقلة / Standalone Features

- ✅ لا تحتاج تثبيت Python
- ✅ لا تحتاج تثبيت مكتبات إضافية
- ✅ تعمل مباشرة من الفلاش ميموري
- ✅ حجم صغير ومحسن
- ✅ سرعة في التشغيل

## 🔐 بيانات الدخول الافتراضية / Default Login

- **اسم المستخدم / Username:** admin
- **كلمة المرور / Password:** admin123

## 📋 متطلبات التشغيل / System Requirements

- Windows 7+ / Linux / macOS
- 100 MB مساحة فارغة
- 512 MB ذاكرة عشوائية

## 🛠️ استكشاف الأخطاء / Troubleshooting

### إذا لم يعمل النظام:
1. تأكد من صلاحيات التشغيل
2. أغلق برامج مكافحة الفيروسات مؤقتاً
3. شغل كمدير (Run as Administrator)

### If the system doesn't work:
1. Check execution permissions
2. Temporarily disable antivirus
3. Run as Administrator

## 📞 الدعم / Support

للدعم والمساعدة، يرجى التواصل مع فريق التطوير.
For support and help, please contact the development team.

---
🇶🇦 صنع بحب في قطر / Made with ❤️ in Qatar
"""
    
    with open(os.path.join(standalone_dir, "README_STANDALONE.md"), "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print("   ✅ تم إنشاء README_STANDALONE.md")

def create_standalone_zip(standalone_dir):
    """Create ZIP for standalone version"""
    import zipfile
    
    zip_filename = f"{standalone_dir}.zip"
    
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(standalone_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, standalone_dir)
                zipf.write(file_path, arc_name)
    
    print(f"   ✅ تم إنشاء {zip_filename}")

if __name__ == "__main__":
    create_standalone_version()

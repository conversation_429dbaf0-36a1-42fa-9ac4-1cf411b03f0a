#!/bin/bash

clear
echo ""
echo "    ╔═══════════════════════════════════════════════════════════╗"
echo "    ║  🇶🇦        نظام إدارة الرواتب القطري        🇶🇦  ║"
echo "    ║           Qatar Salary Management System              ║"
echo "    ║                   النسخة النهائية                    ║"
echo "    ╚═══════════════════════════════════════════════════════════╝"
echo ""
echo "                🚀 تشغيل مباشر - بدون أخطاء 🚀"
echo ""
echo "    ✅ جميع الأخطاء تم إصلاحها"
echo "    ✅ حقل الجنس يعمل بشكل مثالي"
echo "    ✅ أيقونات قطرية جميلة"
echo "    ✅ واجهة عربية متطورة"
echo ""

# Find Python
if command -v python3 &> /dev/null; then
    PYTHON="python3"
elif command -v python &> /dev/null; then
    PYTHON="python"
else
    echo "    ❌ Python غير موجود - يرجى تثبيته"
    exit 1
fi

echo "    ✅ Python موجود"

# Check and install requirements
$PYTHON -c "import flask" &> /dev/null
if [ $? -ne 0 ]; then
    echo "    📦 تثبيت المكتبات..."
    $PYTHON -m pip install flask flask-sqlalchemy &> /dev/null
    echo "    ✅ تم تثبيت المكتبات"
else
    echo "    ✅ المكتبات موجودة"
fi

echo ""
echo "    🚀 جاري تشغيل النظام..."
echo ""
echo "    🌐 العنوان: http://localhost:7474"
echo "    👤 المستخدم: admin"
echo "    🔑 كلمة المرور: admin123"
echo ""

# Open browser
if command -v xdg-open &> /dev/null; then
    xdg-open http://localhost:7474 &
elif command -v open &> /dev/null; then
    open http://localhost:7474 &
fi

# Start application
$PYTHON simple_app.py

echo ""
echo "    ✅ تم إيقاف النظام"

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب جديد - نظام إدارة الرواتب القطري</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #8B1538 0%, #A91B60 50%, #8B1538 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .register-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .register-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 500px;
            width: 100%;
        }
        
        .register-header {
            background: linear-gradient(135deg, #8B1538, #A91B60);
            color: white;
            padding: 30px;
            border-radius: 20px 20px 0 0;
            text-align: center;
        }
        
        .register-header h2 {
            margin: 0;
            font-weight: 600;
            font-size: 1.8rem;
        }
        
        .register-header .subtitle {
            margin-top: 10px;
            opacity: 0.9;
            font-size: 1rem;
        }
        
        .register-body {
            padding: 40px;
        }
        
        .form-floating {
            margin-bottom: 20px;
        }
        
        .form-floating > .form-control {
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 12px 16px;
            height: auto;
            font-size: 1rem;
        }
        
        .form-floating > .form-control:focus {
            border-color: #8B1538;
            box-shadow: 0 0 0 0.2rem rgba(139, 21, 56, 0.25);
        }
        
        .form-floating > label {
            padding: 12px 16px;
            color: #6c757d;
        }
        
        .btn-register {
            background: linear-gradient(135deg, #8B1538, #A91B60);
            border: none;
            border-radius: 12px;
            padding: 15px 30px;
            font-weight: 600;
            font-size: 1.1rem;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(139, 21, 56, 0.3);
        }
        
        .login-link {
            text-align: center;
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }
        
        .login-link a {
            color: #8B1538;
            text-decoration: none;
            font-weight: 600;
        }
        
        .login-link a:hover {
            color: #A91B60;
        }
        
        .alert {
            border-radius: 12px;
            border: none;
            margin-bottom: 20px;
        }
        
        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .alert-success {
            background-color: #d1edff;
            color: #0c5460;
        }
        
        .qatar-flag {
            display: inline-block;
            margin: 0 5px;
        }
        
        .first-user-notice {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 15px;
            border-radius: 12px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .password-requirements {
            font-size: 0.875rem;
            color: #6c757d;
            margin-top: 5px;
        }
        
        .password-requirements ul {
            margin: 5px 0 0 20px;
            padding: 0;
        }
        
        .password-requirements li {
            margin: 2px 0;
        }
    </style>
</head>
<body>
    <div class="register-container">
        <div class="register-card">
            <div class="register-header">
                <h2>
                    <i class="fas fa-user-plus"></i>
                    إنشاء حساب جديد
                </h2>
                <div class="subtitle">
                    نظام إدارة الرواتب القطري <span class="qatar-flag">🇶🇦</span>
                </div>
            </div>
            
            <div class="register-body">
                <!-- First User Notice -->
                <script>
                    // Check if this might be the first user
                    if (window.location.search.includes('first=true') || document.referrer.includes('login')) {
                        document.write(`
                            <div class="first-user-notice">
                                <i class="fas fa-crown"></i>
                                <strong>مرحباً!</strong> أنت أول مستخدم في النظام. سيتم منحك صلاحيات المدير تلقائياً.
                            </div>
                        `);
                    }
                </script>
                
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'check-circle' }}"></i>
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}
                
                <form method="POST">
                    <div class="form-floating">
                        <input type="text" class="form-control" id="username" name="username" 
                               placeholder="اسم المستخدم" required minlength="3">
                        <label for="username">
                            <i class="fas fa-user"></i> اسم المستخدم
                        </label>
                    </div>
                    
                    <div class="form-floating">
                        <input type="email" class="form-control" id="email" name="email" 
                               placeholder="البريد الإلكتروني" required>
                        <label for="email">
                            <i class="fas fa-envelope"></i> البريد الإلكتروني
                        </label>
                    </div>
                    
                    <div class="form-floating">
                        <input type="password" class="form-control" id="password" name="password" 
                               placeholder="كلمة المرور" required minlength="6">
                        <label for="password">
                            <i class="fas fa-lock"></i> كلمة المرور
                        </label>
                        <div class="password-requirements">
                            <strong>متطلبات كلمة المرور:</strong>
                            <ul>
                                <li>أكثر من 6 أحرف</li>
                                <li>يُفضل استخدام أحرف وأرقام</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="form-floating">
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                               placeholder="تأكيد كلمة المرور" required>
                        <label for="confirm_password">
                            <i class="fas fa-lock"></i> تأكيد كلمة المرور
                        </label>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-register">
                        <i class="fas fa-user-plus"></i>
                        إنشاء الحساب
                    </button>
                </form>
                
                <div class="login-link">
                    <p>لديك حساب بالفعل؟ 
                        <a href="{{ url_for('login') }}">
                            <i class="fas fa-sign-in-alt"></i>
                            تسجيل الدخول
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Password confirmation validation
        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('password').value;
            const confirmPassword = this.value;
            
            if (password !== confirmPassword) {
                this.setCustomValidity('كلمة المرور غير متطابقة');
            } else {
                this.setCustomValidity('');
            }
        });
        
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html>

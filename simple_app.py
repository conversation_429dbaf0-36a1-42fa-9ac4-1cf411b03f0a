from flask import Flask, render_template, request, redirect, url_for, flash, session
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, date
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = 'qatar-salary-system-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///qatar_salary_system.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['DEFAULT_CURRENCY'] = 'QAR'
app.config['COUNTRY'] = 'Qatar'

db = SQLAlchemy(app)

# Models
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    role = db.Column(db.String(20), default='user')
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def is_admin(self):
        return self.role == 'admin'

class Worker(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    nationality = db.Column(db.String(50), nullable=False)
    id_number = db.Column(db.String(50), unique=True, nullable=False)
    gender = db.Column(db.String(10), nullable=False)  # ذكر أو أنثى
    job_type = db.Column(db.String(50), nullable=False)
    employment_date = db.Column(db.Date, nullable=False)
    monthly_salary = db.Column(db.Float, nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    salary_payments = db.relationship('SalaryPayment', backref='worker', lazy=True)

class SalaryPayment(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    worker_id = db.Column(db.Integer, db.ForeignKey('worker.id'), nullable=False)
    month = db.Column(db.String(7), nullable=False)
    base_salary = db.Column(db.Float, nullable=False)
    overtime_hours = db.Column(db.Float, default=0)
    overtime_rate = db.Column(db.Float, default=0)
    bonuses = db.Column(db.Float, default=0)
    deductions = db.Column(db.Float, default=0)
    total_amount = db.Column(db.Float, nullable=False)
    payment_date = db.Column(db.Date)
    payment_method = db.Column(db.String(50))
    status = db.Column(db.String(20), default='unpaid')
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def calculate_total(self):
        overtime_amount = self.overtime_hours * self.overtime_rate
        self.total_amount = self.base_salary + overtime_amount + self.bonuses - self.deductions
        return self.total_amount

# Helper functions
def login_required(f):
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

def get_current_user():
    if 'user_id' in session:
        return User.query.get(session['user_id'])
    return None

# Routes
@app.route('/')
@login_required
def dashboard():
    current_user = get_current_user()
    current_month = datetime.now().strftime('%Y-%m')

    # Basic statistics
    total_workers = Worker.query.filter_by(is_active=True).count()
    total_payments_this_month = SalaryPayment.query.filter(
        SalaryPayment.month == current_month
    ).count()
    unpaid_payments = SalaryPayment.query.filter_by(status='unpaid').count()

    # Calculate total amount paid this month
    total_amount_this_month = db.session.query(db.func.sum(SalaryPayment.total_amount)).filter(
        SalaryPayment.month == current_month,
        SalaryPayment.status == 'paid'
    ).scalar() or 0

    # Recent payments
    recent_payments = SalaryPayment.query.order_by(SalaryPayment.created_at.desc()).limit(5).all()

    # Workers by nationality (top 5)
    nationality_stats = db.session.query(
        Worker.nationality,
        db.func.count(Worker.id).label('count')
    ).filter(Worker.is_active == True).group_by(Worker.nationality).order_by(
        db.func.count(Worker.id).desc()
    ).limit(5).all()

    # Workers by job type (top 5)
    job_type_stats = db.session.query(
        Worker.job_type,
        db.func.count(Worker.id).label('count')
    ).filter(Worker.is_active == True).group_by(Worker.job_type).order_by(
        db.func.count(Worker.id).desc()
    ).limit(5).all()

    return render_template('simple_dashboard.html',
                         current_user=current_user,
                         total_workers=total_workers,
                         total_payments_this_month=total_payments_this_month,
                         unpaid_payments=unpaid_payments,
                         total_amount_this_month=total_amount_this_month,
                         recent_payments=recent_payments,
                         nationality_stats=nationality_stats,
                         job_type_stats=job_type_stats)

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        user = User.query.filter_by(username=username).first()
        if user and user.check_password(password) and user.is_active:
            session['user_id'] = user.id
            return redirect(url_for('dashboard'))
        flash('Invalid username or password', 'error')
    
    return render_template('simple_login.html')

@app.route('/logout')
@login_required
def logout():
    session.pop('user_id', None)
    return redirect(url_for('login'))

@app.route('/workers')
@login_required
def workers():
    current_user = get_current_user()
    search = request.args.get('search', '', type=str)
    
    query = Worker.query
    if search:
        query = query.filter(Worker.name.contains(search) | 
                           Worker.nationality.contains(search) |
                           Worker.id_number.contains(search))
    
    workers = query.order_by(Worker.name).all()
    
    return render_template('simple_workers.html', 
                         current_user=current_user,
                         workers=workers, 
                         search=search)

@app.route('/workers/add', methods=['GET', 'POST'])
@login_required
def add_worker():
    current_user = get_current_user()
    if request.method == 'POST':
        # Validate required fields
        name = request.form.get('name', '').strip()
        nationality = request.form.get('nationality', '').strip()
        id_number = request.form.get('id_number', '').strip()
        gender = request.form.get('gender', '').strip()
        job_type = request.form.get('job_type', '').strip()
        employment_date = request.form.get('employment_date', '').strip()
        monthly_salary = request.form.get('monthly_salary', '').strip()

        # Validation
        errors = []

        if not name:
            errors.append('اسم العامل مطلوب')
        elif len(name) < 2:
            errors.append('اسم العامل يجب أن يكون أكثر من حرفين')

        if not nationality:
            errors.append('الجنسية مطلوبة')

        # Only validate gender if the column exists
        if has_gender_column():
            if not gender:
                errors.append('الجنس مطلوب')
            elif gender not in ['ذكر', 'أنثى']:
                errors.append('الجنس يجب أن يكون ذكر أو أنثى')

        if not id_number:
            errors.append('رقم الهوية/الإقامة مطلوب')
        elif len(id_number) < 8 or len(id_number) > 15:
            errors.append('رقم الهوية/الإقامة يجب أن يكون بين 8-15 حرف أو رقم')
        elif not id_number.replace(' ', '').replace('-', '').isalnum():
            errors.append('رقم الهوية/الإقامة يجب أن يحتوي على أرقام وحروف فقط')

        if not job_type:
            errors.append('نوع العمل مطلوب')

        if not employment_date:
            errors.append('تاريخ التوظيف مطلوب')

        if not monthly_salary:
            errors.append('الراتب الشهري مطلوب')
        else:
            try:
                salary_value = float(monthly_salary)
                if salary_value <= 0:
                    errors.append('الراتب الشهري يجب أن يكون أكبر من صفر')
            except ValueError:
                errors.append('الراتب الشهري يجب أن يكون رقم صحيح')

        # Check if ID number already exists
        if id_number and Worker.query.filter_by(id_number=id_number).first():
            errors.append('يوجد عامل آخر بنفس رقم الهوية/الإقامة')

        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('simple_worker_form.html', current_user=current_user, title='إضافة عامل')

        try:
            worker_data = {
                'name': name,
                'nationality': nationality,
                'id_number': id_number,
                'job_type': job_type,
                'employment_date': datetime.strptime(employment_date, '%Y-%m-%d').date(),
                'monthly_salary': float(monthly_salary),
                'is_active': bool(request.form.get('is_active'))
            }

            # Add gender only if column exists
            if has_gender_column():
                worker_data['gender'] = gender

            worker = Worker(**worker_data)

            db.session.add(worker)
            db.session.commit()
            flash('تم إضافة العامل بنجاح', 'success')
            return redirect(url_for('workers'))

        except Exception as e:
            flash('حدث خطأ أثناء إضافة العامل', 'error')
            return render_template('simple_worker_form.html', current_user=current_user, title='إضافة عامل')

    return render_template('simple_worker_form.html', current_user=current_user, title='إضافة عامل')

@app.route('/workers/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_worker(id):
    current_user = get_current_user()
    worker = Worker.query.get_or_404(id)

    if request.method == 'POST':
        # Validate required fields
        name = request.form.get('name', '').strip()
        nationality = request.form.get('nationality', '').strip()
        id_number = request.form.get('id_number', '').strip()
        gender = request.form.get('gender', '').strip()
        job_type = request.form.get('job_type', '').strip()
        employment_date = request.form.get('employment_date', '').strip()
        monthly_salary = request.form.get('monthly_salary', '').strip()

        # Validation
        errors = []

        if not name:
            errors.append('اسم العامل مطلوب')
        elif len(name) < 2:
            errors.append('اسم العامل يجب أن يكون أكثر من حرفين')

        if not nationality:
            errors.append('الجنسية مطلوبة')

        # Only validate gender if the column exists
        if has_gender_column():
            if not gender:
                errors.append('الجنس مطلوب')
            elif gender not in ['ذكر', 'أنثى']:
                errors.append('الجنس يجب أن يكون ذكر أو أنثى')

        if not id_number:
            errors.append('رقم الهوية/الإقامة مطلوب')
        elif len(id_number) < 8 or len(id_number) > 15:
            errors.append('رقم الهوية/الإقامة يجب أن يكون بين 8-15 حرف أو رقم')
        elif not id_number.replace(' ', '').replace('-', '').isalnum():
            errors.append('رقم الهوية/الإقامة يجب أن يحتوي على أرقام وحروف فقط')
        elif Worker.query.filter(Worker.id_number == id_number, Worker.id != id).first():
            errors.append('يوجد عامل آخر بنفس رقم الهوية/الإقامة')

        if not job_type:
            errors.append('نوع العمل مطلوب')

        if not employment_date:
            errors.append('تاريخ التوظيف مطلوب')

        if not monthly_salary:
            errors.append('الراتب الشهري مطلوب')
        else:
            try:
                salary_value = float(monthly_salary)
                if salary_value <= 0:
                    errors.append('الراتب الشهري يجب أن يكون أكبر من صفر')
            except ValueError:
                errors.append('الراتب الشهري يجب أن يكون رقم صحيح')

        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('simple_worker_form.html', current_user=current_user, worker=worker, title='تعديل عامل')

        try:
            worker.name = name
            worker.nationality = nationality
            worker.id_number = id_number

            # Update gender only if column exists
            if has_gender_column():
                worker.gender = gender

            worker.job_type = job_type
            worker.employment_date = datetime.strptime(employment_date, '%Y-%m-%d').date()
            worker.monthly_salary = float(monthly_salary)
            worker.is_active = bool(request.form.get('is_active'))

            db.session.commit()
            flash('تم تحديث العامل بنجاح', 'success')
            return redirect(url_for('workers'))

        except Exception as e:
            flash('حدث خطأ أثناء تحديث العامل', 'error')
            return render_template('simple_worker_form.html', current_user=current_user, worker=worker, title='تعديل عامل')

    return render_template('simple_worker_form.html', current_user=current_user, worker=worker, title='تعديل عامل')

@app.route('/workers/<int:id>/view')
@login_required
def view_worker(id):
    current_user = get_current_user()
    worker = Worker.query.get_or_404(id)
    recent_payments = SalaryPayment.query.filter_by(worker_id=id).order_by(
        SalaryPayment.created_at.desc()).limit(5).all()
    return render_template('simple_worker_view.html', current_user=current_user, worker=worker, recent_payments=recent_payments)

@app.route('/workers/<int:id>/delete', methods=['POST'])
@login_required
def delete_worker(id):
    current_user = get_current_user()
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لحذف العمال', 'error')
        return redirect(url_for('workers'))

    worker = Worker.query.get_or_404(id)

    # Check if worker has salary payments
    if worker.salary_payments:
        flash('لا يمكن حذف العامل لأن لديه رواتب مسجلة', 'error')
        return redirect(url_for('workers'))

    try:
        db.session.delete(worker)
        db.session.commit()
        flash('تم حذف العامل بنجاح', 'success')
    except Exception as e:
        flash('حدث خطأ أثناء حذف العامل', 'error')

    return redirect(url_for('workers'))

@app.route('/salary-payments')
@login_required
def salary_payments():
    current_user = get_current_user()
    status = request.args.get('status', '', type=str)
    worker_id = request.args.get('worker_id', 0, type=int)
    
    query = SalaryPayment.query
    if status:
        query = query.filter_by(status=status)
    if worker_id:
        query = query.filter_by(worker_id=worker_id)
    
    payments = query.order_by(SalaryPayment.created_at.desc()).all()
    workers = Worker.query.filter_by(is_active=True).all()
    
    return render_template('simple_payments.html', 
                         current_user=current_user,
                         payments=payments, 
                         workers=workers,
                         current_status=status,
                         current_worker_id=worker_id)

@app.route('/salary-payments/add', methods=['GET', 'POST'])
@login_required
def add_salary_payment():
    current_user = get_current_user()
    workers = Worker.query.filter_by(is_active=True).all()

    # Get pre-selected values from URL parameters
    preselected_worker_id = request.args.get('worker_id', type=int)
    preselected_month = request.args.get('month', '')

    if request.method == 'POST':
        # Validate required fields
        worker_id = request.form.get('worker_id', '').strip()
        month = request.form.get('month', '').strip()
        base_salary = request.form.get('base_salary', '').strip()
        status = request.form.get('status', '').strip()

        # Optional fields
        overtime_hours = request.form.get('overtime_hours', '0').strip()
        overtime_rate = request.form.get('overtime_rate', '0').strip()
        bonuses = request.form.get('bonuses', '0').strip()
        deductions = request.form.get('deductions', '0').strip()
        payment_date = request.form.get('payment_date', '').strip()
        payment_method = request.form.get('payment_method', '').strip()
        notes = request.form.get('notes', '').strip()

        # Validation
        errors = []

        if not worker_id:
            errors.append('يجب اختيار العامل')
        else:
            try:
                worker_id = int(worker_id)
                worker = Worker.query.get(worker_id)
                if not worker:
                    errors.append('العامل المختار غير موجود')
            except ValueError:
                errors.append('معرف العامل غير صحيح')

        if not month:
            errors.append('الشهر مطلوب')
        elif len(month) != 7 or month[4] != '-':
            errors.append('صيغة الشهر يجب أن تكون YYYY-MM')
        else:
            # Check if payment already exists for this worker and month
            existing_payment = SalaryPayment.query.filter_by(
                worker_id=worker_id, month=month
            ).first()
            if existing_payment:
                errors.append('يوجد راتب مسجل لهذا العامل في نفس الشهر')

        if not base_salary:
            errors.append('الراتب الأساسي مطلوب')
        else:
            try:
                base_salary_value = float(base_salary)
                if base_salary_value <= 0:
                    errors.append('الراتب الأساسي يجب أن يكون أكبر من صفر')
            except ValueError:
                errors.append('الراتب الأساسي يجب أن يكون رقم صحيح')

        if not status:
            errors.append('حالة الدفع مطلوبة')

        # Validate optional numeric fields
        try:
            overtime_hours_value = float(overtime_hours) if overtime_hours else 0
            if overtime_hours_value < 0:
                errors.append('الساعات الإضافية لا يمكن أن تكون سالبة')
        except ValueError:
            errors.append('الساعات الإضافية يجب أن تكون رقم صحيح')

        try:
            overtime_rate_value = float(overtime_rate) if overtime_rate else 0
            if overtime_rate_value < 0:
                errors.append('أجر الساعة الإضافية لا يمكن أن يكون سالب')
        except ValueError:
            errors.append('أجر الساعة الإضافية يجب أن يكون رقم صحيح')

        try:
            bonuses_value = float(bonuses) if bonuses else 0
            if bonuses_value < 0:
                errors.append('المكافآت لا يمكن أن تكون سالبة')
        except ValueError:
            errors.append('المكافآت يجب أن تكون رقم صحيح')

        try:
            deductions_value = float(deductions) if deductions else 0
            if deductions_value < 0:
                errors.append('الخصومات لا يمكن أن تكون سالبة')
        except ValueError:
            errors.append('الخصومات يجب أن تكون رقم صحيح')

        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('simple_payment_form.html',
                                 current_user=current_user,
                                 workers=workers,
                                 title='إضافة راتب')

        try:
            payment = SalaryPayment(
                worker_id=int(worker_id),
                month=month,
                base_salary=float(base_salary),
                overtime_hours=float(overtime_hours) if overtime_hours else 0,
                overtime_rate=float(overtime_rate) if overtime_rate else 0,
                bonuses=float(bonuses) if bonuses else 0,
                deductions=float(deductions) if deductions else 0,
                payment_date=datetime.strptime(payment_date, '%Y-%m-%d').date() if payment_date else None,
                payment_method=payment_method if payment_method else None,
                status=status,
                notes=notes if notes else None
            )
            payment.calculate_total()

            db.session.add(payment)
            db.session.commit()
            flash('تم إضافة الراتب بنجاح', 'success')
            return redirect(url_for('salary_payments'))

        except Exception as e:
            flash('حدث خطأ أثناء إضافة الراتب', 'error')
            return render_template('simple_payment_form.html',
                                 current_user=current_user,
                                 workers=workers,
                                 title='إضافة راتب')

    return render_template('simple_payment_form.html',
                         current_user=current_user,
                         workers=workers,
                         title='إضافة راتب',
                         preselected_worker_id=preselected_worker_id,
                         preselected_month=preselected_month)

@app.route('/salary-payments/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_salary_payment(id):
    current_user = get_current_user()
    payment = SalaryPayment.query.get_or_404(id)
    workers = Worker.query.filter_by(is_active=True).all()

    if request.method == 'POST':
        # Validate required fields
        worker_id = request.form.get('worker_id', '').strip()
        month = request.form.get('month', '').strip()
        base_salary = request.form.get('base_salary', '').strip()
        status = request.form.get('status', '').strip()

        # Optional fields
        overtime_hours = request.form.get('overtime_hours', '0').strip()
        overtime_rate = request.form.get('overtime_rate', '0').strip()
        bonuses = request.form.get('bonuses', '0').strip()
        deductions = request.form.get('deductions', '0').strip()
        payment_date = request.form.get('payment_date', '').strip()
        payment_method = request.form.get('payment_method', '').strip()
        notes = request.form.get('notes', '').strip()

        # Validation
        errors = []

        if not worker_id:
            errors.append('يجب اختيار العامل')
        else:
            try:
                worker_id = int(worker_id)
                worker = Worker.query.get(worker_id)
                if not worker:
                    errors.append('العامل المختار غير موجود')
            except ValueError:
                errors.append('معرف العامل غير صحيح')

        if not month:
            errors.append('الشهر مطلوب')
        elif len(month) != 7 or month[4] != '-':
            errors.append('صيغة الشهر يجب أن تكون YYYY-MM')
        else:
            # Check if payment already exists for this worker and month (excluding current payment)
            existing_payment = SalaryPayment.query.filter(
                SalaryPayment.worker_id == worker_id,
                SalaryPayment.month == month,
                SalaryPayment.id != id
            ).first()
            if existing_payment:
                errors.append('يوجد راتب مسجل لهذا العامل في نفس الشهر')

        if not base_salary:
            errors.append('الراتب الأساسي مطلوب')
        else:
            try:
                base_salary_value = float(base_salary)
                if base_salary_value <= 0:
                    errors.append('الراتب الأساسي يجب أن يكون أكبر من صفر')
            except ValueError:
                errors.append('الراتب الأساسي يجب أن يكون رقم صحيح')

        if not status:
            errors.append('حالة الدفع مطلوبة')

        # Validate optional numeric fields
        try:
            overtime_hours_value = float(overtime_hours) if overtime_hours else 0
            if overtime_hours_value < 0:
                errors.append('الساعات الإضافية لا يمكن أن تكون سالبة')
        except ValueError:
            errors.append('الساعات الإضافية يجب أن تكون رقم صحيح')

        try:
            overtime_rate_value = float(overtime_rate) if overtime_rate else 0
            if overtime_rate_value < 0:
                errors.append('أجر الساعة الإضافية لا يمكن أن يكون سالب')
        except ValueError:
            errors.append('أجر الساعة الإضافية يجب أن يكون رقم صحيح')

        try:
            bonuses_value = float(bonuses) if bonuses else 0
            if bonuses_value < 0:
                errors.append('المكافآت لا يمكن أن تكون سالبة')
        except ValueError:
            errors.append('المكافآت يجب أن تكون رقم صحيح')

        try:
            deductions_value = float(deductions) if deductions else 0
            if deductions_value < 0:
                errors.append('الخصومات لا يمكن أن تكون سالبة')
        except ValueError:
            errors.append('الخصومات يجب أن تكون رقم صحيح')

        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('simple_payment_form.html',
                                 current_user=current_user,
                                 workers=workers,
                                 payment=payment,
                                 title='تعديل راتب')

        try:
            payment.worker_id = int(worker_id)
            payment.month = month
            payment.base_salary = float(base_salary)
            payment.overtime_hours = float(overtime_hours) if overtime_hours else 0
            payment.overtime_rate = float(overtime_rate) if overtime_rate else 0
            payment.bonuses = float(bonuses) if bonuses else 0
            payment.deductions = float(deductions) if deductions else 0
            payment.payment_date = datetime.strptime(payment_date, '%Y-%m-%d').date() if payment_date else None
            payment.payment_method = payment_method if payment_method else None
            payment.status = status
            payment.notes = notes if notes else None
            payment.calculate_total()

            db.session.commit()
            flash('تم تحديث الراتب بنجاح', 'success')
            return redirect(url_for('salary_payments'))

        except Exception as e:
            flash('حدث خطأ أثناء تحديث الراتب', 'error')
            return render_template('simple_payment_form.html',
                                 current_user=current_user,
                                 workers=workers,
                                 payment=payment,
                                 title='تعديل راتب')

    return render_template('simple_payment_form.html',
                         current_user=current_user,
                         workers=workers,
                         payment=payment,
                         title='تعديل راتب')

@app.route('/salary-payments/<int:id>/delete', methods=['POST'])
@login_required
def delete_salary_payment(id):
    current_user = get_current_user()
    if not current_user.is_admin():
        flash('ليس لديك صلاحية لحذف الرواتب', 'error')
        return redirect(url_for('salary_payments'))

    payment = SalaryPayment.query.get_or_404(id)

    try:
        db.session.delete(payment)
        db.session.commit()
        flash('تم حذف الراتب بنجاح', 'success')
    except Exception as e:
        flash('حدث خطأ أثناء حذف الراتب', 'error')

    return redirect(url_for('salary_payments'))

@app.route('/salary-payments/<int:id>/toggle-status', methods=['POST'])
@login_required
def toggle_payment_status(id):
    current_user = get_current_user()
    payment = SalaryPayment.query.get_or_404(id)

    try:
        # Toggle between paid and unpaid
        if payment.status == 'paid':
            payment.status = 'unpaid'
            payment.payment_date = None
            flash('تم تغيير حالة الراتب إلى غير مدفوع', 'success')
        else:
            payment.status = 'paid'
            payment.payment_date = datetime.now().date()
            flash('تم تغيير حالة الراتب إلى مدفوع', 'success')

        db.session.commit()
    except Exception as e:
        flash('حدث خطأ أثناء تغيير حالة الراتب', 'error')

    return redirect(url_for('salary_payments'))

@app.route('/profile')
@login_required
def profile():
    current_user = get_current_user()

    # Get user statistics
    if current_user.is_admin():
        # Admin statistics
        total_users = User.query.count()
        active_users = User.query.filter_by(is_active=True).count()
        total_workers = Worker.query.count()
        total_payments = SalaryPayment.query.count()
        recent_activities = []

        # Recent workers added
        recent_workers = Worker.query.order_by(Worker.created_at.desc()).limit(5).all()
        for worker in recent_workers:
            recent_activities.append({
                'type': 'worker_added',
                'description': f'تم إضافة العامل {worker.name}',
                'date': worker.created_at,
                'icon': 'fas fa-user-plus',
                'color': 'primary'
            })

        # Recent payments
        recent_payments = SalaryPayment.query.order_by(SalaryPayment.created_at.desc()).limit(5).all()
        for payment in recent_payments:
            recent_activities.append({
                'type': 'payment_added',
                'description': f'تم إضافة راتب للعامل {payment.worker.name} - {payment.month}',
                'date': payment.created_at,
                'icon': 'fas fa-money-bill-wave',
                'color': 'success'
            })

        # Sort activities by date
        recent_activities.sort(key=lambda x: x['date'], reverse=True)
        recent_activities = recent_activities[:10]

        stats = {
            'total_users': total_users,
            'active_users': active_users,
            'total_workers': total_workers,
            'total_payments': total_payments,
            'recent_activities': recent_activities
        }
    else:
        # Regular user statistics
        total_workers = Worker.query.count()
        active_workers = Worker.query.filter_by(is_active=True).count()
        total_payments = SalaryPayment.query.count()
        paid_payments = SalaryPayment.query.filter_by(status='paid').count()
        unpaid_payments = SalaryPayment.query.filter_by(status='unpaid').count()

        # Recent activities for regular user
        recent_activities = []

        # Recent workers added
        recent_workers = Worker.query.order_by(Worker.created_at.desc()).limit(3).all()
        for worker in recent_workers:
            recent_activities.append({
                'type': 'worker_added',
                'description': f'تم إضافة العامل {worker.name}',
                'date': worker.created_at,
                'icon': 'fas fa-user-plus',
                'color': 'primary'
            })

        # Recent payments
        recent_payments = SalaryPayment.query.order_by(SalaryPayment.created_at.desc()).limit(7).all()
        for payment in recent_payments:
            recent_activities.append({
                'type': 'payment_added',
                'description': f'تم إضافة راتب للعامل {payment.worker.name} - {payment.month}',
                'date': payment.created_at,
                'icon': 'fas fa-money-bill-wave',
                'color': 'success'
            })

        # Sort activities by date
        recent_activities.sort(key=lambda x: x['date'], reverse=True)
        recent_activities = recent_activities[:8]

        stats = {
            'total_workers': total_workers,
            'active_workers': active_workers,
            'total_payments': total_payments,
            'paid_payments': paid_payments,
            'unpaid_payments': unpaid_payments,
            'recent_activities': recent_activities
        }

    return render_template('profile.html', current_user=current_user, stats=stats)

@app.route('/profile/edit', methods=['GET', 'POST'])
@login_required
def edit_profile():
    current_user = get_current_user()

    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        email = request.form.get('email', '').strip()
        current_password = request.form.get('current_password', '').strip()
        new_password = request.form.get('new_password', '').strip()
        confirm_password = request.form.get('confirm_password', '').strip()

        # Validation
        errors = []

        if not username:
            errors.append('اسم المستخدم مطلوب')
        elif len(username) < 3:
            errors.append('اسم المستخدم يجب أن يكون أكثر من 3 أحرف')
        elif User.query.filter(User.username == username, User.id != current_user.id).first():
            errors.append('اسم المستخدم موجود مسبقاً')

        if not email:
            errors.append('البريد الإلكتروني مطلوب')
        elif User.query.filter(User.email == email, User.id != current_user.id).first():
            errors.append('البريد الإلكتروني موجود مسبقاً')

        # Password validation (if changing password)
        if new_password or current_password:
            if not current_password:
                errors.append('كلمة المرور الحالية مطلوبة لتغيير كلمة المرور')
            elif not current_user.check_password(current_password):
                errors.append('كلمة المرور الحالية غير صحيحة')
            elif not new_password:
                errors.append('كلمة المرور الجديدة مطلوبة')
            elif len(new_password) < 6:
                errors.append('كلمة المرور الجديدة يجب أن تكون أكثر من 6 أحرف')
            elif new_password != confirm_password:
                errors.append('تأكيد كلمة المرور غير متطابق')

        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('profile_edit.html', current_user=current_user)

        try:
            current_user.username = username
            current_user.email = email

            if new_password:
                current_user.set_password(new_password)

            db.session.commit()
            flash('تم تحديث الملف الشخصي بنجاح', 'success')
            return redirect(url_for('profile'))

        except Exception as e:
            flash('حدث خطأ أثناء تحديث الملف الشخصي', 'error')
            return render_template('profile_edit.html', current_user=current_user)

    return render_template('profile_edit.html', current_user=current_user)

@app.route('/settings')
@login_required
def settings():
    current_user = get_current_user()
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    # Get system statistics
    stats = {
        'total_users': User.query.count(),
        'active_users': User.query.filter_by(is_active=True).count(),
        'admin_users': User.query.filter_by(role='admin').count(),
        'total_workers': Worker.query.count(),
        'active_workers': Worker.query.filter_by(is_active=True).count(),
        'total_payments': SalaryPayment.query.count(),
        'paid_payments': SalaryPayment.query.filter_by(status='paid').count(),
        'unpaid_payments': SalaryPayment.query.filter_by(status='unpaid').count(),
        'database_size': get_database_size(),
        'last_backup': get_last_backup_date(),
    }

    # Get recent system activities
    recent_activities = []

    # Recent users
    recent_users = User.query.order_by(User.created_at.desc()).limit(3).all()
    for user in recent_users:
        recent_activities.append({
            'type': 'user_created',
            'description': f'تم إنشاء مستخدم جديد: {user.username}',
            'date': user.created_at,
            'icon': 'fas fa-user-plus',
            'color': 'primary'
        })

    # Recent workers
    recent_workers = Worker.query.order_by(Worker.created_at.desc()).limit(3).all()
    for worker in recent_workers:
        recent_activities.append({
            'type': 'worker_created',
            'description': f'تم إضافة عامل جديد: {worker.name}',
            'date': worker.created_at,
            'icon': 'fas fa-user-tie',
            'color': 'success'
        })

    # Recent payments
    recent_payments = SalaryPayment.query.order_by(SalaryPayment.created_at.desc()).limit(4).all()
    for payment in recent_payments:
        recent_activities.append({
            'type': 'payment_created',
            'description': f'تم إضافة راتب: {payment.worker.name} - {payment.month}',
            'date': payment.created_at,
            'icon': 'fas fa-money-bill-wave',
            'color': 'warning'
        })

    # Sort activities by date
    recent_activities.sort(key=lambda x: x['date'], reverse=True)
    recent_activities = recent_activities[:10]

    return render_template('settings.html', current_user=current_user, stats=stats, recent_activities=recent_activities)

@app.route('/settings/system', methods=['GET', 'POST'])
@login_required
def system_settings():
    current_user = get_current_user()
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        action = request.form.get('action')

        if action == 'backup_database':
            try:
                backup_result = create_database_backup()
                if backup_result:
                    flash('تم إنشاء نسخة احتياطية من قاعدة البيانات بنجاح', 'success')
                else:
                    flash('فشل في إنشاء النسخة الاحتياطية', 'error')
            except Exception as e:
                flash('حدث خطأ أثناء إنشاء النسخة الاحتياطية', 'error')

        elif action == 'clear_logs':
            try:
                clear_system_logs()
                flash('تم مسح سجلات النظام بنجاح', 'success')
            except Exception as e:
                flash('حدث خطأ أثناء مسح السجلات', 'error')

        elif action == 'optimize_database':
            try:
                optimize_database()
                flash('تم تحسين قاعدة البيانات بنجاح', 'success')
            except Exception as e:
                flash('حدث خطأ أثناء تحسين قاعدة البيانات', 'error')

        return redirect(url_for('system_settings'))

    # Get system information
    system_info = get_system_info()

    # Get database statistics
    db_stats = {
        'total_users': User.query.count(),
        'total_workers': Worker.query.count(),
        'total_payments': SalaryPayment.query.count(),
        'database_size': get_database_size()
    }

    return render_template('settings_system.html', current_user=current_user, system_info=system_info, db_stats=db_stats)

@app.route('/settings/security', methods=['GET', 'POST'])
@login_required
def security_settings():
    current_user = get_current_user()
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        action = request.form.get('action')

        if action == 'reset_all_sessions':
            try:
                # This would reset all user sessions in a real implementation
                flash('تم إعادة تعيين جميع جلسات المستخدمين', 'success')
            except Exception as e:
                flash('حدث خطأ أثناء إعادة تعيين الجلسات', 'error')

        elif action == 'deactivate_inactive_users':
            try:
                # Deactivate users who haven't logged in for 90 days
                from datetime import timedelta
                cutoff_date = datetime.now() - timedelta(days=90)
                inactive_users = User.query.filter(User.created_at < cutoff_date, User.is_active == True).all()

                count = 0
                for user in inactive_users:
                    if user.id != current_user.id:  # Don't deactivate current user
                        user.is_active = False
                        count += 1

                if count > 0:
                    db.session.commit()
                    flash(f'تم إلغاء تفعيل {count} مستخدم غير نشط', 'success')
                else:
                    flash('لا يوجد مستخدمين غير نشطين لإلغاء تفعيلهم', 'info')
            except Exception as e:
                flash('حدث خطأ أثناء إلغاء تفعيل المستخدمين', 'error')

        return redirect(url_for('security_settings'))

    # Get security information
    security_info = get_security_info()

    return render_template('settings_security.html', current_user=current_user, security_info=security_info)

# Helper functions for settings
def get_database_size():
    """Get database file size in MB"""
    try:
        import os
        size = os.path.getsize('qatar_salary_system.db')
        return round(size / (1024 * 1024), 2)
    except:
        return 0

def get_last_backup_date():
    """Get last backup date"""
    try:
        import os
        import glob
        backup_files = glob.glob('backup_*.db')
        if backup_files:
            latest_backup = max(backup_files, key=os.path.getctime)
            return datetime.fromtimestamp(os.path.getctime(latest_backup))
        return None
    except:
        return None

def create_database_backup():
    """Create database backup"""
    try:
        import shutil
        backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        shutil.copy2('qatar_salary_system.db', backup_name)
        return True
    except:
        return False

def clear_system_logs():
    """Clear system logs (placeholder)"""
    # In a real implementation, this would clear log files
    pass

def optimize_database():
    """Optimize database (placeholder)"""
    try:
        # Run VACUUM command to optimize SQLite database
        db.session.execute('VACUUM')
        db.session.commit()
        return True
    except:
        return False

def get_system_info():
    """Get system information"""
    import platform
    try:
        import psutil
        return {
            'python_version': platform.python_version(),
            'system': platform.system(),
            'platform': platform.platform(),
            'cpu_count': psutil.cpu_count(),
            'memory_total': round(psutil.virtual_memory().total / (1024**3), 2),
            'memory_available': round(psutil.virtual_memory().available / (1024**3), 2),
            'disk_usage': round(psutil.disk_usage('.').percent, 1),
            'last_backup': get_last_backup_date(),
        }
    except ImportError:
        return {
            'python_version': platform.python_version(),
            'system': platform.system(),
            'platform': platform.platform(),
            'cpu_count': 'غير متاح',
            'memory_total': 'غير متاح',
            'memory_available': 'غير متاح',
            'disk_usage': 0,
            'last_backup': get_last_backup_date(),
        }

def get_security_info():
    """Get security information"""
    from datetime import timedelta

    # Get users by activity
    active_users = User.query.filter_by(is_active=True).count()
    inactive_users = User.query.filter_by(is_active=False).count()
    admin_users = User.query.filter_by(role='admin').count()

    # Get recent login attempts (placeholder)
    recent_logins = 0  # In real implementation, track login attempts

    return {
        'active_users': active_users,
        'inactive_users': inactive_users,
        'admin_users': admin_users,
        'recent_logins': recent_logins,
        'password_policy': 'كلمة المرور يجب أن تكون أكثر من 6 أحرف',
        'session_timeout': '24 ساعة',
        'last_backup': get_last_backup_date(),
    }

# User Management Routes
@app.route('/users')
@login_required
def users():
    current_user = get_current_user()
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    users = User.query.all()
    return render_template('users/list.html', current_user=current_user, users=users)

@app.route('/users/add', methods=['GET', 'POST'])
@login_required
def add_user():
    current_user = get_current_user()
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        email = request.form.get('email', '').strip()
        password = request.form.get('password', '').strip()
        role = request.form.get('role', '').strip()
        is_active = bool(request.form.get('is_active'))

        # Validation
        errors = []

        if not username:
            errors.append('اسم المستخدم مطلوب')
        elif len(username) < 3:
            errors.append('اسم المستخدم يجب أن يكون أكثر من 3 أحرف')
        elif User.query.filter_by(username=username).first():
            errors.append('اسم المستخدم موجود مسبقاً')

        if not email:
            errors.append('البريد الإلكتروني مطلوب')
        elif User.query.filter_by(email=email).first():
            errors.append('البريد الإلكتروني موجود مسبقاً')

        if not password:
            errors.append('كلمة المرور مطلوبة')
        elif len(password) < 6:
            errors.append('كلمة المرور يجب أن تكون أكثر من 6 أحرف')

        if not role:
            errors.append('الدور مطلوب')

        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('users/form.html', current_user=current_user, title='إضافة مستخدم')

        try:
            user = User(
                username=username,
                email=email,
                role=role,
                is_active=is_active
            )
            user.set_password(password)

            db.session.add(user)
            db.session.commit()
            flash('تم إضافة المستخدم بنجاح', 'success')
            return redirect(url_for('users'))

        except Exception as e:
            flash('حدث خطأ أثناء إضافة المستخدم', 'error')
            return render_template('users/form.html', current_user=current_user, title='إضافة مستخدم')

    return render_template('users/form.html', current_user=current_user, title='إضافة مستخدم')

@app.route('/users/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_user(id):
    current_user = get_current_user()
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    user = User.query.get_or_404(id)

    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        email = request.form.get('email', '').strip()
        password = request.form.get('password', '').strip()
        role = request.form.get('role', '').strip()
        is_active = bool(request.form.get('is_active'))

        # Validation
        errors = []

        if not username:
            errors.append('اسم المستخدم مطلوب')
        elif len(username) < 3:
            errors.append('اسم المستخدم يجب أن يكون أكثر من 3 أحرف')
        elif User.query.filter(User.username == username, User.id != id).first():
            errors.append('اسم المستخدم موجود مسبقاً')

        if not email:
            errors.append('البريد الإلكتروني مطلوب')
        elif User.query.filter(User.email == email, User.id != id).first():
            errors.append('البريد الإلكتروني موجود مسبقاً')

        if password and len(password) < 6:
            errors.append('كلمة المرور يجب أن تكون أكثر من 6 أحرف')

        if not role:
            errors.append('الدور مطلوب')

        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('users/form.html', current_user=current_user, user=user, title='تعديل مستخدم')

        try:
            user.username = username
            user.email = email
            user.role = role
            user.is_active = is_active

            if password:
                user.set_password(password)

            db.session.commit()
            flash('تم تحديث المستخدم بنجاح', 'success')
            return redirect(url_for('users'))

        except Exception as e:
            flash('حدث خطأ أثناء تحديث المستخدم', 'error')
            return render_template('users/form.html', current_user=current_user, user=user, title='تعديل مستخدم')

    return render_template('users/form.html', current_user=current_user, user=user, title='تعديل مستخدم')

@app.route('/users/<int:id>/delete', methods=['POST'])
@login_required
def delete_user(id):
    current_user = get_current_user()
    if not current_user.is_admin():
        flash('ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
        return redirect(url_for('dashboard'))

    if id == current_user.id:
        flash('لا يمكنك حذف حسابك الخاص', 'error')
        return redirect(url_for('users'))

    user = User.query.get_or_404(id)

    try:
        db.session.delete(user)
        db.session.commit()
        flash('تم حذف المستخدم بنجاح', 'success')
    except Exception as e:
        flash('حدث خطأ أثناء حذف المستخدم', 'error')

    return redirect(url_for('users'))

# Reports Routes
@app.route('/reports')
@login_required
def reports():
    current_user = get_current_user()
    return render_template('reports/index.html', current_user=current_user)

@app.route('/reports/workers')
@login_required
def workers_report():
    current_user = get_current_user()

    # Workers statistics
    total_workers = Worker.query.filter_by(is_active=True).count()
    inactive_workers = Worker.query.filter_by(is_active=False).count()

    # Workers by nationality
    nationality_stats = db.session.query(
        Worker.nationality,
        db.func.count(Worker.id).label('count')
    ).filter(Worker.is_active == True).group_by(Worker.nationality).order_by(
        db.func.count(Worker.id).desc()
    ).all()

    # Workers by job type
    job_type_stats = db.session.query(
        Worker.job_type,
        db.func.count(Worker.id).label('count')
    ).filter(Worker.is_active == True).group_by(Worker.job_type).order_by(
        db.func.count(Worker.id).desc()
    ).all()

    # Recent workers
    recent_workers = Worker.query.order_by(Worker.created_at.desc()).limit(10).all()

    return render_template('reports/workers.html',
                         current_user=current_user,
                         total_workers=total_workers,
                         inactive_workers=inactive_workers,
                         nationality_stats=nationality_stats,
                         job_type_stats=job_type_stats,
                         recent_workers=recent_workers)

@app.route('/reports/salaries')
@login_required
def salaries_report():
    current_user = get_current_user()

    # Get filter parameters
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    status = request.args.get('status', '')
    worker_id = request.args.get('worker_id', 0, type=int)

    # Base query
    query = SalaryPayment.query

    # Apply filters
    if start_date:
        query = query.filter(SalaryPayment.month >= start_date)
    if end_date:
        query = query.filter(SalaryPayment.month <= end_date)
    if status:
        query = query.filter(SalaryPayment.status == status)
    if worker_id:
        query = query.filter(SalaryPayment.worker_id == worker_id)

    payments = query.order_by(SalaryPayment.month.desc()).all()

    # Calculate statistics
    total_payments = len(payments)
    total_amount = sum(p.total_amount for p in payments if p.status == 'paid')
    paid_count = len([p for p in payments if p.status == 'paid'])
    unpaid_count = len([p for p in payments if p.status == 'unpaid'])
    partial_count = len([p for p in payments if p.status == 'partial'])

    # Monthly statistics
    monthly_stats = {}
    for payment in payments:
        month = payment.month
        if month not in monthly_stats:
            monthly_stats[month] = {
                'total_amount': 0,
                'paid_amount': 0,
                'count': 0,
                'paid_count': 0
            }
        monthly_stats[month]['count'] += 1
        monthly_stats[month]['total_amount'] += payment.total_amount
        if payment.status == 'paid':
            monthly_stats[month]['paid_count'] += 1
            monthly_stats[month]['paid_amount'] += payment.total_amount

    workers = Worker.query.filter_by(is_active=True).all()

    return render_template('reports/salaries.html',
                         current_user=current_user,
                         payments=payments,
                         total_payments=total_payments,
                         total_amount=total_amount,
                         paid_count=paid_count,
                         unpaid_count=unpaid_count,
                         partial_count=partial_count,
                         monthly_stats=monthly_stats,
                         workers=workers,
                         start_date=start_date,
                         end_date=end_date,
                         current_status=status,
                         current_worker_id=worker_id)

@app.route('/reports/monthly')
@login_required
def monthly_report():
    current_user = get_current_user()

    # Get month parameter
    month = request.args.get('month', datetime.now().strftime('%Y-%m'))

    # Get payments for the month
    payments = SalaryPayment.query.filter_by(month=month).all()

    # Calculate statistics
    total_workers_paid = len(set(p.worker_id for p in payments))
    total_amount = sum(p.total_amount for p in payments)
    paid_amount = sum(p.total_amount for p in payments if p.status == 'paid')
    unpaid_amount = sum(p.total_amount for p in payments if p.status == 'unpaid')

    # Workers without payment this month
    workers_with_payment = set(p.worker_id for p in payments)
    all_active_workers = Worker.query.filter_by(is_active=True).all()
    workers_without_payment = [w for w in all_active_workers if w.id not in workers_with_payment]

    return render_template('reports/monthly.html',
                         current_user=current_user,
                         month=month,
                         payments=payments,
                         total_workers_paid=total_workers_paid,
                         total_amount=total_amount,
                         paid_amount=paid_amount,
                         unpaid_amount=unpaid_amount,
                         workers_without_payment=workers_without_payment)

@app.route('/reports/export/<report_type>')
@login_required
def export_report(report_type):
    current_user = get_current_user()

    if report_type == 'workers':
        # Export workers data
        workers = Worker.query.all()

        # Create CSV content
        csv_content = "الاسم,الجنسية,رقم الهوية,نوع العمل,تاريخ التوظيف,الراتب الشهري,الحالة,تاريخ الإضافة\n"
        for worker in workers:
            csv_content += f"{worker.name},{worker.nationality},{worker.id_number},{worker.job_type},{worker.employment_date},{worker.monthly_salary},{'نشط' if worker.is_active else 'غير نشط'},{worker.created_at.strftime('%Y-%m-%d')}\n"

        # Return CSV file
        from flask import Response
        return Response(
            csv_content,
            mimetype="text/csv",
            headers={"Content-disposition": f"attachment; filename=workers_report_{datetime.now().strftime('%Y%m%d')}.csv"}
        )

    elif report_type == 'salaries':
        # Export salaries data
        payments = SalaryPayment.query.all()

        # Create CSV content
        csv_content = "العامل,الشهر,الراتب الأساسي,الساعات الإضافية,المكافآت,الخصومات,إجمالي المبلغ,تاريخ الدفع,طريقة الدفع,الحالة\n"
        for payment in payments:
            csv_content += f"{payment.worker.name},{payment.month},{payment.base_salary},{payment.overtime_hours},{payment.bonuses},{payment.deductions},{payment.total_amount},{payment.payment_date or 'غير محدد'},{payment.payment_method or 'غير محدد'},{payment.status}\n"

        # Return CSV file
        from flask import Response
        return Response(
            csv_content,
            mimetype="text/csv",
            headers={"Content-disposition": f"attachment; filename=salaries_report_{datetime.now().strftime('%Y%m%d')}.csv"}
        )

    flash('نوع التقرير غير صحيح', 'error')
    return redirect(url_for('reports'))

def has_gender_column():
    """Check if gender column exists in worker table"""
    try:
        db.session.execute('SELECT gender FROM worker LIMIT 1')
        return True
    except Exception:
        return False

@app.context_processor
def inject_ticker_data():
    """Inject ticker data for all templates"""
    try:
        # Get recent salary payments with worker names
        if has_gender_column():
            recent_payments = db.session.query(
                SalaryPayment.id,
                Worker.name,
                Worker.gender,
                SalaryPayment.total_salary,
                SalaryPayment.month,
                SalaryPayment.status,
                SalaryPayment.created_at
            ).join(Worker).order_by(SalaryPayment.created_at.desc()).limit(15).all()
        else:
            recent_payments = db.session.query(
                SalaryPayment.id,
                Worker.name,
                SalaryPayment.total_salary,
                SalaryPayment.month,
                SalaryPayment.status,
                SalaryPayment.created_at
            ).join(Worker).order_by(SalaryPayment.created_at.desc()).limit(15).all()

        ticker_items = []
        for payment in recent_payments:
            status_text = {
                'paid': 'مدفوع',
                'unpaid': 'غير مدفوع',
                'partial': 'جزئي'
            }.get(payment.status, 'غير محدد')

            status_color = {
                'paid': 'success',
                'unpaid': 'danger',
                'partial': 'warning'
            }.get(payment.status, 'secondary')

            ticker_item = {
                'worker_name': payment.name,
                'salary': payment.total_salary,
                'month': payment.month,
                'status': status_text,
                'status_color': status_color,
                'created_at': payment.created_at
            }

            # Add gender if column exists
            if has_gender_column():
                ticker_item['worker_gender'] = getattr(payment, 'gender', 'ذكر')
            else:
                ticker_item['worker_gender'] = 'ذكر'  # Default value

            ticker_items.append(ticker_item)

        return dict(ticker_data=ticker_items)
    except:
        return dict(ticker_data=[])

def init_db():
    """Initialize database with default data"""
    db.create_all()

    # Check if gender column exists and add it if not
    try:
        # Try to access gender column
        db.session.execute('SELECT gender FROM worker LIMIT 1')
    except Exception as e:
        if "no such column" in str(e):
            # Add gender column to existing database
            try:
                db.session.execute('ALTER TABLE worker ADD COLUMN gender VARCHAR(10) DEFAULT "ذكر"')
                db.session.commit()
                print("Added gender column to worker table")
            except Exception as alter_error:
                print(f"Error adding gender column: {alter_error}")

    # Create admin user if not exists
    if not User.query.filter_by(role='admin').first():
        admin = User(
            username='admin',
            email='<EMAIL>',
            role='admin',
            is_active=True
        )
        admin.set_password('admin123')
        db.session.add(admin)
        db.session.commit()
        print("Default admin user created: admin / admin123")

if __name__ == '__main__':
    with app.app_context():
        init_db()
    app.run(debug=True, host='0.0.0.0', port=7474)

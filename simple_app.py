from flask import Flask, render_template, request, redirect, url_for, flash, session
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, date
import os

app = Flask(__name__)
app.config['SECRET_KEY'] = 'qatar-salary-system-2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///qatar_salary_system.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['DEFAULT_CURRENCY'] = 'QAR'
app.config['COUNTRY'] = 'Qatar'

db = SQLAlchemy(app)

# Models
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    role = db.Column(db.String(20), default='user')
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def is_admin(self):
        return self.role == 'admin'

class Worker(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    nationality = db.Column(db.String(50), nullable=False)
    id_number = db.Column(db.String(50), unique=True, nullable=False)
    job_type = db.Column(db.String(50), nullable=False)
    employment_date = db.Column(db.Date, nullable=False)
    monthly_salary = db.Column(db.Float, nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    salary_payments = db.relationship('SalaryPayment', backref='worker', lazy=True)

class SalaryPayment(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    worker_id = db.Column(db.Integer, db.ForeignKey('worker.id'), nullable=False)
    month = db.Column(db.String(7), nullable=False)
    base_salary = db.Column(db.Float, nullable=False)
    overtime_hours = db.Column(db.Float, default=0)
    overtime_rate = db.Column(db.Float, default=0)
    bonuses = db.Column(db.Float, default=0)
    deductions = db.Column(db.Float, default=0)
    total_amount = db.Column(db.Float, nullable=False)
    payment_date = db.Column(db.Date)
    payment_method = db.Column(db.String(50))
    status = db.Column(db.String(20), default='unpaid')
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def calculate_total(self):
        overtime_amount = self.overtime_hours * self.overtime_rate
        self.total_amount = self.base_salary + overtime_amount + self.bonuses - self.deductions
        return self.total_amount

# Helper functions
def login_required(f):
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    decorated_function.__name__ = f.__name__
    return decorated_function

def get_current_user():
    if 'user_id' in session:
        return User.query.get(session['user_id'])
    return None

# Routes
@app.route('/')
@login_required
def dashboard():
    current_user = get_current_user()
    current_month = datetime.now().strftime('%Y-%m')

    # Basic statistics
    total_workers = Worker.query.filter_by(is_active=True).count()
    total_payments_this_month = SalaryPayment.query.filter(
        SalaryPayment.month == current_month
    ).count()
    unpaid_payments = SalaryPayment.query.filter_by(status='unpaid').count()

    # Calculate total amount paid this month
    total_amount_this_month = db.session.query(db.func.sum(SalaryPayment.total_amount)).filter(
        SalaryPayment.month == current_month,
        SalaryPayment.status == 'paid'
    ).scalar() or 0

    # Recent payments
    recent_payments = SalaryPayment.query.order_by(SalaryPayment.created_at.desc()).limit(5).all()

    # Workers by nationality (top 5)
    nationality_stats = db.session.query(
        Worker.nationality,
        db.func.count(Worker.id).label('count')
    ).filter(Worker.is_active == True).group_by(Worker.nationality).order_by(
        db.func.count(Worker.id).desc()
    ).limit(5).all()

    # Workers by job type (top 5)
    job_type_stats = db.session.query(
        Worker.job_type,
        db.func.count(Worker.id).label('count')
    ).filter(Worker.is_active == True).group_by(Worker.job_type).order_by(
        db.func.count(Worker.id).desc()
    ).limit(5).all()

    return render_template('simple_dashboard.html',
                         current_user=current_user,
                         total_workers=total_workers,
                         total_payments_this_month=total_payments_this_month,
                         unpaid_payments=unpaid_payments,
                         total_amount_this_month=total_amount_this_month,
                         recent_payments=recent_payments,
                         nationality_stats=nationality_stats,
                         job_type_stats=job_type_stats)

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        user = User.query.filter_by(username=username).first()
        if user and user.check_password(password) and user.is_active:
            session['user_id'] = user.id
            return redirect(url_for('dashboard'))
        flash('Invalid username or password', 'error')
    
    return render_template('simple_login.html')

@app.route('/logout')
@login_required
def logout():
    session.pop('user_id', None)
    return redirect(url_for('login'))

@app.route('/workers')
@login_required
def workers():
    current_user = get_current_user()
    search = request.args.get('search', '', type=str)
    
    query = Worker.query
    if search:
        query = query.filter(Worker.name.contains(search) | 
                           Worker.nationality.contains(search) |
                           Worker.id_number.contains(search))
    
    workers = query.order_by(Worker.name).all()
    
    return render_template('simple_workers.html', 
                         current_user=current_user,
                         workers=workers, 
                         search=search)

@app.route('/workers/add', methods=['GET', 'POST'])
@login_required
def add_worker():
    current_user = get_current_user()
    if request.method == 'POST':
        # Validate required fields
        name = request.form.get('name', '').strip()
        nationality = request.form.get('nationality', '').strip()
        id_number = request.form.get('id_number', '').strip()
        job_type = request.form.get('job_type', '').strip()
        employment_date = request.form.get('employment_date', '').strip()
        monthly_salary = request.form.get('monthly_salary', '').strip()

        # Validation
        errors = []

        if not name:
            errors.append('اسم العامل مطلوب')
        elif len(name) < 2:
            errors.append('اسم العامل يجب أن يكون أكثر من حرفين')

        if not nationality:
            errors.append('الجنسية مطلوبة')

        if not id_number:
            errors.append('رقم الهوية/الإقامة مطلوب')
        elif len(id_number) < 8 or len(id_number) > 15:
            errors.append('رقم الهوية/الإقامة يجب أن يكون بين 8-15 حرف أو رقم')
        elif not id_number.replace(' ', '').replace('-', '').isalnum():
            errors.append('رقم الهوية/الإقامة يجب أن يحتوي على أرقام وحروف فقط')

        if not job_type:
            errors.append('نوع العمل مطلوب')

        if not employment_date:
            errors.append('تاريخ التوظيف مطلوب')

        if not monthly_salary:
            errors.append('الراتب الشهري مطلوب')
        else:
            try:
                salary_value = float(monthly_salary)
                if salary_value <= 0:
                    errors.append('الراتب الشهري يجب أن يكون أكبر من صفر')
            except ValueError:
                errors.append('الراتب الشهري يجب أن يكون رقم صحيح')

        # Check if ID number already exists
        if id_number and Worker.query.filter_by(id_number=id_number).first():
            errors.append('يوجد عامل آخر بنفس رقم الهوية/الإقامة')

        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('simple_worker_form.html', current_user=current_user, title='إضافة عامل')

        try:
            worker = Worker(
                name=name,
                nationality=nationality,
                id_number=id_number,
                job_type=job_type,
                employment_date=datetime.strptime(employment_date, '%Y-%m-%d').date(),
                monthly_salary=float(monthly_salary),
                is_active=bool(request.form.get('is_active'))
            )

            db.session.add(worker)
            db.session.commit()
            flash('تم إضافة العامل بنجاح', 'success')
            return redirect(url_for('workers'))

        except Exception as e:
            flash('حدث خطأ أثناء إضافة العامل', 'error')
            return render_template('simple_worker_form.html', current_user=current_user, title='إضافة عامل')

    return render_template('simple_worker_form.html', current_user=current_user, title='إضافة عامل')

@app.route('/salary-payments')
@login_required
def salary_payments():
    current_user = get_current_user()
    status = request.args.get('status', '', type=str)
    worker_id = request.args.get('worker_id', 0, type=int)
    
    query = SalaryPayment.query
    if status:
        query = query.filter_by(status=status)
    if worker_id:
        query = query.filter_by(worker_id=worker_id)
    
    payments = query.order_by(SalaryPayment.created_at.desc()).all()
    workers = Worker.query.filter_by(is_active=True).all()
    
    return render_template('simple_payments.html', 
                         current_user=current_user,
                         payments=payments, 
                         workers=workers,
                         current_status=status,
                         current_worker_id=worker_id)

@app.route('/salary-payments/add', methods=['GET', 'POST'])
@login_required
def add_salary_payment():
    current_user = get_current_user()
    workers = Worker.query.filter_by(is_active=True).all()

    if request.method == 'POST':
        # Validate required fields
        worker_id = request.form.get('worker_id', '').strip()
        month = request.form.get('month', '').strip()
        base_salary = request.form.get('base_salary', '').strip()
        status = request.form.get('status', '').strip()

        # Optional fields
        overtime_hours = request.form.get('overtime_hours', '0').strip()
        overtime_rate = request.form.get('overtime_rate', '0').strip()
        bonuses = request.form.get('bonuses', '0').strip()
        deductions = request.form.get('deductions', '0').strip()
        payment_date = request.form.get('payment_date', '').strip()
        payment_method = request.form.get('payment_method', '').strip()
        notes = request.form.get('notes', '').strip()

        # Validation
        errors = []

        if not worker_id:
            errors.append('يجب اختيار العامل')
        else:
            try:
                worker_id = int(worker_id)
                worker = Worker.query.get(worker_id)
                if not worker:
                    errors.append('العامل المختار غير موجود')
            except ValueError:
                errors.append('معرف العامل غير صحيح')

        if not month:
            errors.append('الشهر مطلوب')
        elif len(month) != 7 or month[4] != '-':
            errors.append('صيغة الشهر يجب أن تكون YYYY-MM')
        else:
            # Check if payment already exists for this worker and month
            existing_payment = SalaryPayment.query.filter_by(
                worker_id=worker_id, month=month
            ).first()
            if existing_payment:
                errors.append('يوجد راتب مسجل لهذا العامل في نفس الشهر')

        if not base_salary:
            errors.append('الراتب الأساسي مطلوب')
        else:
            try:
                base_salary_value = float(base_salary)
                if base_salary_value <= 0:
                    errors.append('الراتب الأساسي يجب أن يكون أكبر من صفر')
            except ValueError:
                errors.append('الراتب الأساسي يجب أن يكون رقم صحيح')

        if not status:
            errors.append('حالة الدفع مطلوبة')

        # Validate optional numeric fields
        try:
            overtime_hours_value = float(overtime_hours) if overtime_hours else 0
            if overtime_hours_value < 0:
                errors.append('الساعات الإضافية لا يمكن أن تكون سالبة')
        except ValueError:
            errors.append('الساعات الإضافية يجب أن تكون رقم صحيح')

        try:
            overtime_rate_value = float(overtime_rate) if overtime_rate else 0
            if overtime_rate_value < 0:
                errors.append('أجر الساعة الإضافية لا يمكن أن يكون سالب')
        except ValueError:
            errors.append('أجر الساعة الإضافية يجب أن يكون رقم صحيح')

        try:
            bonuses_value = float(bonuses) if bonuses else 0
            if bonuses_value < 0:
                errors.append('المكافآت لا يمكن أن تكون سالبة')
        except ValueError:
            errors.append('المكافآت يجب أن تكون رقم صحيح')

        try:
            deductions_value = float(deductions) if deductions else 0
            if deductions_value < 0:
                errors.append('الخصومات لا يمكن أن تكون سالبة')
        except ValueError:
            errors.append('الخصومات يجب أن تكون رقم صحيح')

        if errors:
            for error in errors:
                flash(error, 'error')
            return render_template('simple_payment_form.html',
                                 current_user=current_user,
                                 workers=workers,
                                 title='إضافة راتب')

        try:
            payment = SalaryPayment(
                worker_id=int(worker_id),
                month=month,
                base_salary=float(base_salary),
                overtime_hours=float(overtime_hours) if overtime_hours else 0,
                overtime_rate=float(overtime_rate) if overtime_rate else 0,
                bonuses=float(bonuses) if bonuses else 0,
                deductions=float(deductions) if deductions else 0,
                payment_date=datetime.strptime(payment_date, '%Y-%m-%d').date() if payment_date else None,
                payment_method=payment_method if payment_method else None,
                status=status,
                notes=notes if notes else None
            )
            payment.calculate_total()

            db.session.add(payment)
            db.session.commit()
            flash('تم إضافة الراتب بنجاح', 'success')
            return redirect(url_for('salary_payments'))

        except Exception as e:
            flash('حدث خطأ أثناء إضافة الراتب', 'error')
            return render_template('simple_payment_form.html',
                                 current_user=current_user,
                                 workers=workers,
                                 title='إضافة راتب')

    return render_template('simple_payment_form.html',
                         current_user=current_user,
                         workers=workers,
                         title='إضافة راتب')

def init_db():
    """Initialize database with default data"""
    db.create_all()
    
    # Create admin user if not exists
    if not User.query.filter_by(role='admin').first():
        admin = User(
            username='admin',
            email='<EMAIL>',
            role='admin',
            is_active=True
        )
        admin.set_password('admin123')
        db.session.add(admin)
        db.session.commit()
        print("Default admin user created: admin / admin123")

if __name__ == '__main__':
    with app.app_context():
        init_db()
    app.run(debug=True, host='0.0.0.0', port=7474)

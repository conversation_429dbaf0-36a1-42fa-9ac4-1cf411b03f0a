# دليل الإعدادات ⚙️

## نظرة عامة
تم إنشاء نظام شامل للإعدادات يتيح للمديرين إدارة النظام والأمان وقاعدة البيانات بطريقة متقدمة وآمنة.

## 🔐 الوصول للإعدادات

### الصلاحيات المطلوبة:
- **المديرين فقط** - الإعدادات متاحة للمديرين حصرياً
- **حماية تلقائية** - المستخدمين العاديين يتم توجيههم للوحة التحكم

### طرق الوصول:
1. **من القائمة الجانبية:** اضغط على "الإعدادات" ⚙️
2. **رابط مباشر:** http://localhost:7474/settings
3. **من الملف الشخصي:** زر "إدارة المستخدمين" ثم "الإعدادات"

## 📋 أقسام الإعدادات

### 1. **الصفحة الرئيسية للإعدادات** 🏠
- **نظرة عامة شاملة** على حالة النظام
- **إحصائيات مفصلة** للمستخدمين والعمال والرواتب
- **حالة النظام** مع مؤشرات ملونة
- **النشاطات الأخيرة** في النظام
- **روابط سريعة** لجميع أقسام الإعدادات

### 2. **إعدادات النظام** 🖥️
- **إدارة قاعدة البيانات** والنسخ الاحتياطية
- **تحسين الأداء** ومسح السجلات
- **معلومات النظام** التفصيلية
- **جدولة الصيانة** والتوصيات

### 3. **إعدادات الأمان** 🛡️
- **إدارة المستخدمين** والجلسات
- **سياسات الأمان** وكلمات المرور
- **مراقبة النشاطات** المشبوهة
- **توصيات أمنية** متقدمة

## 🖥️ إعدادات النظام

### الميزات المتاحة:

#### 1. **إدارة قاعدة البيانات** 💾
- **إنشاء نسخة احتياطية:**
  - نسخ تلقائي لقاعدة البيانات
  - تسمية بالتاريخ والوقت
  - حفظ في مجلد المشروع
  - تأكيد قبل الإنشاء

- **تحسين قاعدة البيانات:**
  - تشغيل أمر VACUUM لـ SQLite
  - تقليل حجم قاعدة البيانات
  - تحسين الأداء
  - إعادة تنظيم البيانات

- **مسح السجلات:**
  - مسح سجلات النظام القديمة
  - توفير مساحة تخزين
  - تحسين الأداء

#### 2. **معلومات النظام** 📊
- **إصدار Python** المستخدم
- **نظام التشغيل** والمنصة
- **عدد المعالجات** المتاحة
- **الذاكرة الإجمالية** والمتاحة
- **استخدام القرص** مع مؤشر بصري

#### 3. **إحصائيات قاعدة البيانات** 📈
- **عدد المستخدمين** في النظام
- **عدد العمال** المسجلين
- **عدد الرواتب** المدخلة
- **حجم قاعدة البيانات** بالميجابايت

#### 4. **جدولة الصيانة** 📅
- **توصيات الصيانة:**
  - نسخ احتياطية أسبوعية
  - تحسين شهري لقاعدة البيانات
  - مسح السجلات كل 3 أشهر
  - مراجعة أمنية شهرية

- **تتبع آخر النشاطات:**
  - تاريخ آخر نسخة احتياطية
  - آخر تحسين لقاعدة البيانات
  - آخر مسح للسجلات

## 🛡️ إعدادات الأمان

### الميزات المتاحة:

#### 1. **أمان المستخدمين** 👥
- **إعادة تعيين جميع الجلسات:**
  - إجبار جميع المستخدمين على تسجيل الدخول مرة أخرى
  - مفيد في حالة الاشتباه في اختراق
  - تأكيد مزدوج قبل التنفيذ

- **إلغاء تفعيل المستخدمين غير النشطين:**
  - البحث عن المستخدمين غير النشطين لأكثر من 90 يوم
  - إلغاء تفعيلهم تلقائياً
  - حماية من ترك الحسابات مفتوحة

#### 2. **معلومات الأمان** 📋
- **المستخدمين النشطين** وغير النشطين
- **عدد المديرين** في النظام
- **محاولات الدخول الأخيرة**
- **سياسة كلمات المرور** الحالية
- **مدة انتهاء الجلسة**

#### 3. **إحصائيات الأمان** 📊
- **مستخدمين نشطين** - مؤشر أخضر
- **مستخدمين غير نشطين** - مؤشر رمادي
- **مديرين** - مؤشر أحمر
- **محاولات دخول حديثة** - مؤشر أزرق

#### 4. **سياسات الأمان** 📜
- **السياسات الحالية:**
  - كلمة المرور أكثر من 6 أحرف
  - انتهاء الجلسة بعد 24 ساعة
  - تشفير كلمات المرور
  - حماية من SQL Injection

- **التوصيات الأمنية:**
  - كلمات مرور قوية ✅
  - تشفير كلمات المرور ✅
  - انتهاء الجلسات ✅
  - حماية من الثغرات ✅

#### 5. **تنبيهات الأمان** ⚠️
- **تنبيهات تلقائية:**
  - وجود مستخدمين غير نشطين
  - عدم وجود نسخ احتياطية
  - توصيات لتحسين الأمان

- **سجل الأمان:**
  - تسجيل دخول المديرين
  - إنشاء مستخدمين جدد
  - تغيير كلمات المرور

## 🎨 التصميم والواجهة

### الألوان المستخدمة:
- **🔵 أزرق** - إعدادات النظام والمعلومات العامة
- **🔴 أحمر** - إعدادات الأمان والتحذيرات
- **🟢 أخضر** - الحالات الإيجابية والنجاح
- **🟡 أصفر** - التنبيهات والتحذيرات
- **⚫ رمادي** - المعلومات الثانوية

### التخطيط:
- **بطاقات منظمة** لكل قسم
- **أيقونات واضحة** لكل ميزة
- **مؤشرات بصرية** للحالات
- **أزرار تفاعلية** مع تأثيرات

## 🚀 كيفية الاستخدام

### للوصول للإعدادات:
1. سجل دخولك كمدير
2. اضغط على "الإعدادات" في القائمة الجانبية
3. اختر القسم المطلوب

### لإنشاء نسخة احتياطية:
1. اذهب إلى "إعدادات النظام"
2. اضغط "إنشاء نسخة احتياطية"
3. أكد العملية
4. ستحفظ النسخة في مجلد المشروع

### لتحسين قاعدة البيانات:
1. اذهب إلى "إعدادات النظام"
2. اضغط "تحسين قاعدة البيانات"
3. أكد العملية
4. انتظر انتهاء التحسين

### لإعادة تعيين الجلسات:
1. اذهب إلى "إعدادات الأمان"
2. اضغط "إعادة تعيين الجلسات"
3. أكد العملية
4. سيتم تسجيل خروج جميع المستخدمين

### لإلغاء تفعيل المستخدمين غير النشطين:
1. اذهب إلى "إعدادات الأمان"
2. اضغط "إلغاء تفعيل المستخدمين غير النشطين"
3. أكد العملية
4. سيتم إلغاء تفعيل المستخدمين القدامى

## 🔒 الأمان والحماية

### حماية الوصول:
- ✅ **مديرين فقط** - فحص الصلاحيات في كل صفحة
- ✅ **إعادة توجيه آمنة** - المستخدمين العاديين يتم توجيههم
- ✅ **رسائل خطأ واضحة** - إعلام المستخدم بعدم وجود صلاحية

### حماية العمليات:
- ✅ **تأكيد مزدوج** - نوافذ تأكيد قبل العمليات الحساسة
- ✅ **معالجة الأخطاء** - try/catch لجميع العمليات
- ✅ **رسائل واضحة** - إعلام المستخدم بنتيجة العملية

### حماية البيانات:
- ✅ **نسخ احتياطية آمنة** - حفظ في مكان آمن
- ✅ **تشفير كلمات المرور** - bcrypt hashing
- ✅ **حماية قاعدة البيانات** - منع SQL injection

## 📱 التوافق والاستجابة

### أحجام الشاشات:
- **الهواتف** - تخطيط عمودي مع بطاقات مكدسة
- **الأجهزة اللوحية** - تخطيط شبكي متوسط
- **أجهزة الكمبيوتر** - تخطيط أفقي كامل

### المتصفحات:
- ✅ **Chrome** - دعم كامل
- ✅ **Firefox** - دعم كامل
- ✅ **Safari** - دعم كامل
- ✅ **Edge** - دعم كامل

## 🔧 الميزات التقنية

### إدارة قاعدة البيانات:
- **SQLite VACUUM** - تحسين وضغط قاعدة البيانات
- **نسخ احتياطية تلقائية** - مع تسمية بالتاريخ
- **مراقبة الحجم** - عرض حجم قاعدة البيانات

### معلومات النظام:
- **Python platform module** - معلومات النظام الأساسية
- **psutil library** - معلومات الأداء (اختيارية)
- **معالجة الأخطاء** - في حالة عدم توفر المكتبات

### الأمان:
- **Flask session management** - إدارة الجلسات
- **bcrypt password hashing** - تشفير كلمات المرور
- **SQLAlchemy ORM** - حماية من SQL injection

## 📊 الإحصائيات المعروضة

### في الصفحة الرئيسية:
- **إجمالي المستخدمين** مع تفصيل النشطين والمديرين
- **إجمالي العمال** مع تفصيل النشطين وغير النشطين
- **إجمالي الرواتب** مع تفصيل المدفوع والمعلق
- **حجم قاعدة البيانات** مع تاريخ آخر نسخة احتياطية

### في إعدادات النظام:
- **معلومات الأجهزة** - المعالج والذاكرة والقرص
- **معلومات البرمجيات** - Python ونظام التشغيل
- **إحصائيات قاعدة البيانات** - عدد السجلات والحجم

### في إعدادات الأمان:
- **إحصائيات المستخدمين** - نشطين وغير نشطين ومديرين
- **محاولات الدخول** - العدد والتوقيت
- **سياسات الأمان** - القواعد المطبقة

## 🎯 التوصيات

### للمديرين:
- **راجع الإعدادات** أسبوعياً
- **أنشئ نسخ احتياطية** بانتظام
- **راقب المستخدمين** غير النشطين
- **حدث سياسات الأمان** حسب الحاجة

### للصيانة:
- **نسخ احتياطية** كل أسبوع
- **تحسين قاعدة البيانات** كل شهر
- **مسح السجلات** كل 3 أشهر
- **مراجعة الأمان** كل شهر

### للأمان:
- **راقب محاولات الدخول** المشبوهة
- **ألغِ تفعيل المستخدمين** غير المطلوبين
- **حدث كلمات المرور** بانتظام
- **راجع صلاحيات المديرين** دورياً

---

**نظام الإعدادات الآن يوفر إدارة شاملة ومتقدمة للنظام والأمان! 🇶🇦**

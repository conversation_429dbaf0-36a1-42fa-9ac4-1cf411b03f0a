#!/bin/bash

clear
echo ""
echo "    ╔═══════════════════════════════════════════════════════════╗"
echo "    ║  🇶🇦        نظام إدارة الرواتب القطري        🇶🇦  ║"
echo "    ║           Qatar Salary Management System              ║"
echo "    ╚═══════════════════════════════════════════════════════════╝"
echo ""
echo "                🚀 تشغيل مباشر - بدون تعقيدات 🚀"
echo ""

# Find Python
if command -v python3 &> /dev/null; then
    PYTHON="python3"
elif command -v python &> /dev/null; then
    PYTHON="python"
else
    echo "    ❌ Python غير موجود - يرجى تثبيته"
    exit 1
fi

# Quick install if needed
$PYTHON -c "import flask" &> /dev/null
if [ $? -ne 0 ]; then
    echo "    📦 تثبيت سريع للمكتبات..."
    $PYTHON -m pip install flask flask-sqlalchemy &> /dev/null
fi

echo "    ✅ جاري التشغيل..."
echo ""
echo "    🌐 العنوان: http://localhost:7474"
echo "    👤 المستخدم: admin"
echo "    🔑 كلمة المرور: admin123"
echo ""

# Start and open browser
if command -v xdg-open &> /dev/null; then
    xdg-open http://localhost:7474 &
elif command -v open &> /dev/null; then
    open http://localhost:7474 &
fi

$PYTHON simple_app.py

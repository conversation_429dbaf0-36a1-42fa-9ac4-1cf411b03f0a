('E:\\Visual Studio Code\\065\\dist\\نظام_إدارة_الرواتب_القطري.exe',
 True,
 False,
 False,
 ['E:\\Visual Studio '
  'Code\\065\\Qatar_Salary_System_Portable/qatar_salary_icon.ico'],
 None,
 <PERSON>als<PERSON>,
 <PERSON>als<PERSON>,
 b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<assembly xmlns='
 b'"urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">\n  <trustInfo x'
 b'mlns="urn:schemas-microsoft-com:asm.v3">\n    <security>\n      <requested'
 b'Privileges>\n        <requestedExecutionLevel level="asInvoker" uiAccess='
 b'"false"/>\n      </requestedPrivileges>\n    </security>\n  </trustInfo>\n  '
 b'<compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">\n    <'
 b'application>\n      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f'
 b'0}"/>\n      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>\n '
 b'     <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>\n      <s'
 b'upportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>\n      <supporte'
 b'dOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>\n    </application>\n  <'
 b'/compatibility>\n  <application xmlns="urn:schemas-microsoft-com:asm.v3">'
 b'\n    <windowsSettings>\n      <longPathAware xmlns="http://schemas.micros'
 b'oft.com/SMI/2016/WindowsSettings">true</longPathAware>\n    </windowsSett'
 b'ings>\n  </application>\n  <dependency>\n    <dependentAssembly>\n      <ass'
 b'emblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" version='
 b'"6.0.0.0" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" langua'
 b'ge="*"/>\n    </dependentAssembly>\n  </dependency>\n</assembly>',
 True,
 False,
 None,
 None,
 None,
 'E:\\Visual Studio '
 'Code\\065\\build\\standalone\\نظام_إدارة_الرواتب_القطري.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'E:\\Visual Studio Code\\065\\build\\standalone\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'E:\\Visual Studio Code\\065\\build\\standalone\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'E:\\Visual Studio '
   'Code\\065\\build\\standalone\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'E:\\Visual Studio '
   'Code\\065\\build\\standalone\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'E:\\Visual Studio '
   'Code\\065\\build\\standalone\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'E:\\Visual Studio '
   'Code\\065\\build\\standalone\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('simple_app', 'E:\\Visual Studio Code\\065\\simple_app.py', 'PYSOURCE'),
  ('python313.dll', 'C:\\Program Files\\Python313\\python313.dll', 'BINARY'),
  ('_decimal.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('select.pyd', 'C:\\Program Files\\Python313\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Program Files\\Python313\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_lzma.pyd', 'C:\\Program Files\\Python313\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'C:\\Program Files\\Python313\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_overlapped.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'C:\\Program Files\\Python313\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_queue.pyd', 'C:\\Program Files\\Python313\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Program Files\\Python313\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('greenlet\\_greenlet.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\greenlet\\_greenlet.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\util.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\cyextension\\util.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\resultproxy.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\cyextension\\resultproxy.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\processors.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\cyextension\\processors.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\immutabledict.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\cyextension\\immutabledict.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('sqlalchemy\\cyextension\\collections.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\sqlalchemy\\cyextension\\collections.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('_uuid.pyd', 'C:\\Program Files\\Python313\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('_sqlite3.pyd',
   'C:\\Program Files\\Python313\\DLLs\\_sqlite3.pyd',
   'EXTENSION'),
  ('markupsafe\\_speedups.cp313-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\markupsafe\\_speedups.cp313-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('_wmi.pyd', 'C:\\Program Files\\Python313\\DLLs\\_wmi.pyd', 'EXTENSION'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll',
   'C:\\Program Files\\Python313\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3.dll',
   'C:\\Program Files\\Python313\\DLLs\\libcrypto-3.dll',
   'BINARY'),
  ('libssl-3.dll',
   'C:\\Program Files\\Python313\\DLLs\\libssl-3.dll',
   'BINARY'),
  ('libffi-8.dll',
   'C:\\Program Files\\Python313\\DLLs\\libffi-8.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('sqlite3.dll', 'C:\\Program Files\\Python313\\DLLs\\sqlite3.dll', 'BINARY'),
  ('python3.dll', 'C:\\Program Files\\Python313\\python3.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Program Files\\Python313\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\ucrtbase.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-kernel32-legacy-l1-1-1.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-core-kernel32-legacy-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-2-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-core-sysinfo-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-1.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-core-fibers-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Program Files\\Eclipse '
   'Adoptium\\jre-8.0.452.9-hotspot\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('static\\css\\style.css',
   'E:\\Visual Studio Code\\065\\static\\css\\style.css',
   'DATA'),
  ('templates\\admin\\settings.html',
   'E:\\Visual Studio Code\\065\\templates\\admin\\settings.html',
   'DATA'),
  ('templates\\admin\\users.html',
   'E:\\Visual Studio Code\\065\\templates\\admin\\users.html',
   'DATA'),
  ('templates\\base.html',
   'E:\\Visual Studio Code\\065\\templates\\base.html',
   'DATA'),
  ('templates\\dashboard.html',
   'E:\\Visual Studio Code\\065\\templates\\dashboard.html',
   'DATA'),
  ('templates\\login.html',
   'E:\\Visual Studio Code\\065\\templates\\login.html',
   'DATA'),
  ('templates\\profile.html',
   'E:\\Visual Studio Code\\065\\templates\\profile.html',
   'DATA'),
  ('templates\\profile_edit.html',
   'E:\\Visual Studio Code\\065\\templates\\profile_edit.html',
   'DATA'),
  ('templates\\reports\\index.html',
   'E:\\Visual Studio Code\\065\\templates\\reports\\index.html',
   'DATA'),
  ('templates\\reports\\monthly.html',
   'E:\\Visual Studio Code\\065\\templates\\reports\\monthly.html',
   'DATA'),
  ('templates\\reports\\salaries.html',
   'E:\\Visual Studio Code\\065\\templates\\reports\\salaries.html',
   'DATA'),
  ('templates\\reports\\workers.html',
   'E:\\Visual Studio Code\\065\\templates\\reports\\workers.html',
   'DATA'),
  ('templates\\salary_payments\\form.html',
   'E:\\Visual Studio Code\\065\\templates\\salary_payments\\form.html',
   'DATA'),
  ('templates\\salary_payments\\list.html',
   'E:\\Visual Studio Code\\065\\templates\\salary_payments\\list.html',
   'DATA'),
  ('templates\\salary_payments\\view.html',
   'E:\\Visual Studio Code\\065\\templates\\salary_payments\\view.html',
   'DATA'),
  ('templates\\settings.html',
   'E:\\Visual Studio Code\\065\\templates\\settings.html',
   'DATA'),
  ('templates\\settings_security.html',
   'E:\\Visual Studio Code\\065\\templates\\settings_security.html',
   'DATA'),
  ('templates\\settings_system.html',
   'E:\\Visual Studio Code\\065\\templates\\settings_system.html',
   'DATA'),
  ('templates\\simple_base.html',
   'E:\\Visual Studio Code\\065\\templates\\simple_base.html',
   'DATA'),
  ('templates\\simple_dashboard.html',
   'E:\\Visual Studio Code\\065\\templates\\simple_dashboard.html',
   'DATA'),
  ('templates\\simple_login.html',
   'E:\\Visual Studio Code\\065\\templates\\simple_login.html',
   'DATA'),
  ('templates\\simple_payment_form.html',
   'E:\\Visual Studio Code\\065\\templates\\simple_payment_form.html',
   'DATA'),
  ('templates\\simple_payments.html',
   'E:\\Visual Studio Code\\065\\templates\\simple_payments.html',
   'DATA'),
  ('templates\\simple_worker_form.html',
   'E:\\Visual Studio Code\\065\\templates\\simple_worker_form.html',
   'DATA'),
  ('templates\\simple_worker_view.html',
   'E:\\Visual Studio Code\\065\\templates\\simple_worker_view.html',
   'DATA'),
  ('templates\\simple_workers.html',
   'E:\\Visual Studio Code\\065\\templates\\simple_workers.html',
   'DATA'),
  ('templates\\users\\form.html',
   'E:\\Visual Studio Code\\065\\templates\\users\\form.html',
   'DATA'),
  ('templates\\users\\list.html',
   'E:\\Visual Studio Code\\065\\templates\\users\\list.html',
   'DATA'),
  ('templates\\workers\\form.html',
   'E:\\Visual Studio Code\\065\\templates\\workers\\form.html',
   'DATA'),
  ('templates\\workers\\list.html',
   'E:\\Visual Studio Code\\065\\templates\\workers\\list.html',
   'DATA'),
  ('templates\\workers\\view.html',
   'E:\\Visual Studio Code\\065\\templates\\workers\\view.html',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\RECORD',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\INSTALLER',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\WHEEL',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\REQUESTED',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\LICENSE',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\top_level.txt',
   'DATA'),
  ('setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\importlib_metadata-8.0.0.dist-info\\METADATA',
   'DATA'),
  ('setuptools\\_vendor\\jaraco\\text\\Lorem ipsum.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\setuptools\\_vendor\\jaraco\\text\\Lorem '
   'ipsum.txt',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug-2.3.7.dist-info\\INSTALLER',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\LICENSE.md',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline3-3.5.4.dist-info\\LICENSE.md',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline3-3.5.4.dist-info\\INSTALLER',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline3-3.5.4.dist-info\\WHEEL',
   'DATA'),
  ('click-8.2.1.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click-8.2.1.dist-info\\METADATA',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline3-3.5.4.dist-info\\METADATA',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous-2.2.0.dist-info\\RECORD',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\MarkupSafe-3.0.2.dist-info\\top_level.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\MarkupSafe-3.0.2.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\LICENSE.rst',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug-2.3.7.dist-info\\LICENSE.rst',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug-2.3.7.dist-info\\METADATA',
   'DATA'),
  ('flask-2.3.3.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask-2.3.3.dist-info\\RECORD',
   'DATA'),
  ('flask-2.3.3.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask-2.3.3.dist-info\\INSTALLER',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous-2.2.0.dist-info\\WHEEL',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug-2.3.7.dist-info\\RECORD',
   'DATA'),
  ('click-8.2.1.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click-8.2.1.dist-info\\WHEEL',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\MarkupSafe-3.0.2.dist-info\\LICENSE.txt',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\MarkupSafe-3.0.2.dist-info\\RECORD',
   'DATA'),
  ('flask-2.3.3.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask-2.3.3.dist-info\\WHEEL',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous-2.2.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('flask-2.3.3.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask-2.3.3.dist-info\\REQUESTED',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\REQUESTED',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug-2.3.7.dist-info\\REQUESTED',
   'DATA'),
  ('flask-2.3.3.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask-2.3.3.dist-info\\METADATA',
   'DATA'),
  ('click-8.2.1.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click-8.2.1.dist-info\\RECORD',
   'DATA'),
  ('click-8.2.1.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click-8.2.1.dist-info\\INSTALLER',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\top_level.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline3-3.5.4.dist-info\\top_level.txt',
   'DATA'),
  ('flask-2.3.3.dist-info\\LICENSE.rst',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask-2.3.3.dist-info\\LICENSE.rst',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\MarkupSafe-3.0.2.dist-info\\INSTALLER',
   'DATA'),
  ('werkzeug-2.3.7.dist-info\\WHEEL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\werkzeug-2.3.7.dist-info\\WHEEL',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous-2.2.0.dist-info\\METADATA',
   'DATA'),
  ('MarkupSafe-3.0.2.dist-info\\METADATA',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\MarkupSafe-3.0.2.dist-info\\METADATA',
   'DATA'),
  ('itsdangerous-2.2.0.dist-info\\INSTALLER',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\itsdangerous-2.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\click-8.2.1.dist-info\\licenses\\LICENSE.txt',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\RECORD',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\pyreadline3-3.5.4.dist-info\\RECORD',
   'DATA'),
  ('flask-2.3.3.dist-info\\entry_points.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\flask-2.3.3.dist-info\\entry_points.txt',
   'DATA'),
  ('base_library.zip',
   'E:\\Visual Studio Code\\065\\build\\standalone\\base_library.zip',
   'DATA')],
 [],
 False,
 False,
 **********,
 [('run.exe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\PyInstaller\\bootloader\\Windows-64bit-intel\\run.exe',
   'EXECUTABLE')],
 'C:\\Program Files\\Python313\\python313.dll')

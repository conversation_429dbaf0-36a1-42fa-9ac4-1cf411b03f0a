{% extends "base.html" %}

{% block title %}System Settings - Salary System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4"><i class="fas fa-cogs"></i> System Settings</h1>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-sliders-h"></i> General Settings</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label class="form-label">Company/Sponsor Name</label>
                        <input type="text" class="form-control" name="company_name" 
                               value="Domestic Workers Management System">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Default Currency</label>
                        <select class="form-select" name="default_currency">
                            <option value="SAR" selected>Saudi Riyal (SAR)</option>
                            <option value="USD">US Dollar (USD)</option>
                            <option value="EUR">Euro (EUR)</option>
                            <option value="AED">UAE Dirham (AED)</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Default Language</label>
                        <select class="form-select" name="default_language">
                            <option value="en" selected>English</option>
                            <option value="ar">Arabic</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Default Overtime Rate (per hour)</label>
                        <div class="input-group">
                            <input type="number" class="form-control" name="overtime_rate" 
                                   value="15.00" step="0.01" min="0">
                            <span class="input-group-text">SAR</span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Default Vacation Days (per year)</label>
                        <input type="number" class="form-control" name="vacation_days" 
                               value="30" min="0">
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Save Settings
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> System Information</h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <th>Version:</th>
                        <td>1.0.0</td>
                    </tr>
                    <tr>
                        <th>Database:</th>
                        <td>SQLite</td>
                    </tr>
                    <tr>
                        <th>Framework:</th>
                        <td>Flask</td>
                    </tr>
                    <tr>
                        <th>Language Support:</th>
                        <td>English, Arabic</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-tools"></i> System Tools</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-info">
                        <i class="fas fa-database"></i> Backup Database
                    </button>
                    <button class="btn btn-outline-warning">
                        <i class="fas fa-upload"></i> Restore Database
                    </button>
                    <button class="btn btn-outline-secondary">
                        <i class="fas fa-broom"></i> Clear Cache
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

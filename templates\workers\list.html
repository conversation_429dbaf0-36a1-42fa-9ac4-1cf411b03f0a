{% extends "base.html" %}

{% block title %}Workers - Salary System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-users"></i> Workers Management</h1>
            <a href="{{ url_for('add_worker') }}" class="btn btn-primary">
                <i class="fas fa-user-plus"></i> Add Worker
            </a>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="row mb-3">
    <div class="col-md-6">
        <form method="GET" class="d-flex">
            <input type="text" name="search" class="form-control" placeholder="Search by name, nationality, or ID..." value="{{ search }}">
            <button type="submit" class="btn btn-outline-secondary ms-2">
                <i class="fas fa-search"></i>
            </button>
            {% if search %}
            <a href="{{ url_for('workers') }}" class="btn btn-outline-danger ms-2">
                <i class="fas fa-times"></i>
            </a>
            {% endif %}
        </form>
    </div>
</div>

<!-- Workers Table -->
<div class="card">
    <div class="card-body">
        {% if workers.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>Photo</th>
                        <th>Name</th>
                        <th>Nationality</th>
                        <th>ID Number</th>
                        <th>Job Type</th>
                        <th>Employment Date</th>
                        <th>Monthly Salary</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for worker in workers.items %}
                    <tr>
                        <td>
                            {% if worker.photo_filename %}
                                <img src="{{ url_for('uploaded_file', filename='photos/' + worker.photo_filename) }}" 
                                     class="rounded-circle" width="40" height="40" alt="Photo">
                            {% else %}
                                <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center" 
                                     style="width: 40px; height: 40px;">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                            {% endif %}
                        </td>
                        <td>
                            <strong>{{ worker.name }}</strong>
                        </td>
                        <td>{{ worker.nationality }}</td>
                        <td>{{ worker.id_number }}</td>
                        <td>
                            <span class="badge bg-info">{{ worker.job_type.replace('_', ' ').title() }}</span>
                        </td>
                        <td>{{ worker.employment_date.strftime('%Y-%m-%d') }}</td>
                        <td>{{ "%.2f"|format(worker.monthly_salary) }} SAR</td>
                        <td>
                            {% if worker.is_active %}
                                <span class="badge bg-success">Active</span>
                            {% else %}
                                <span class="badge bg-secondary">Inactive</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('view_worker', id=worker.id) }}" 
                                   class="btn btn-outline-info" title="View">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('edit_worker', id=worker.id) }}" 
                                   class="btn btn-outline-primary" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('worker_salary_history', id=worker.id) }}" 
                                   class="btn btn-outline-success" title="Salary History">
                                    <i class="fas fa-money-bill-wave"></i>
                                </a>
                                {% if current_user.is_admin() %}
                                <button type="button" class="btn btn-outline-danger" 
                                        onclick="confirmDelete({{ worker.id }}, '{{ worker.name }}')" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if workers.pages > 1 %}
        <nav aria-label="Workers pagination">
            <ul class="pagination justify-content-center">
                {% if workers.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('workers', page=workers.prev_num, search=search) }}">Previous</a>
                    </li>
                {% endif %}
                
                {% for page_num in workers.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != workers.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('workers', page=page_num, search=search) }}">{{ page_num }}</a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if workers.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('workers', page=workers.next_num, search=search) }}">Next</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">No workers found</h4>
            {% if search %}
                <p class="text-muted">No workers match your search criteria.</p>
                <a href="{{ url_for('workers') }}" class="btn btn-secondary">Clear Search</a>
            {% else %}
                <p class="text-muted">Start by adding your first worker.</p>
                <a href="{{ url_for('add_worker') }}" class="btn btn-primary">
                    <i class="fas fa-user-plus"></i> Add Worker
                </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete worker <strong id="workerName"></strong>?</p>
                <p class="text-danger"><small>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(workerId, workerName) {
    document.getElementById('workerName').textContent = workerName;
    document.getElementById('deleteForm').action = '/workers/' + workerId + '/delete';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}

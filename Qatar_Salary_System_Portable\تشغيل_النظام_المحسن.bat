@echo off
chcp 65001 >nul
title نظام إدارة الرواتب القطري - Qatar Salary Management System
color 0B

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🇶🇦 نظام إدارة الرواتب القطري 🇶🇦                    ║
echo ║                Qatar Salary Management System                ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo ┌──────────────────────────────────────────────────────────────┐
echo │  💼 نظام شامل لإدارة رواتب العمال في دولة قطر                │
echo │  🚀 نسخة محمولة - تعمل مباشرة من الفلاش ميموري               │
echo │  🌐 واجهة عربية جميلة ومتجاوبة                              │
echo └──────────────────────────────────────────────────────────────┘
echo.

REM Check if Python is installed
echo 🔍 فحص متطلبات النظام...
python --version >nul 2>&1
if errorlevel 1 (
    echo.
    echo ❌ Python غير مثبت على النظام
    echo 📥 يرجى تثبيت Python من: https://python.org
    echo 💡 بعد التثبيت، شغل هذا الملف مرة أخرى
    echo.
    pause
    exit /b 1
)

echo ✅ Python موجود ومثبت
echo.

REM Check if requirements are installed
echo 📦 فحص المكتبات المطلوبة...
python -c "import flask" >nul 2>&1
if errorlevel 1 (
    echo 🔄 جاري تثبيت المكتبات المطلوبة...
    echo    هذا قد يستغرق دقيقة أو دقيقتين...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المكتبات
        echo 🌐 تأكد من الاتصال بالإنترنت وحاول مرة أخرى
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت جميع المكتبات بنجاح
) else (
    echo ✅ جميع المكتبات موجودة ومثبتة
)

echo.
echo ┌──────────────────────────────────────────────────────────────┐
echo │  🚀 جاري تشغيل النظام...                                     │
echo │                                                              │
echo │  🌐 سيتم فتح النظام في المتصفح على العنوان:                │
echo │     http://localhost:7474                                    │
echo │                                                              │
echo │  🔐 بيانات الدخول الافتراضية:                              │
echo │     اسم المستخدم: admin                                     │
echo │     كلمة المرور: admin123                                   │
echo │                                                              │
echo │  🛑 للإيقاف اضغط Ctrl+C                                     │
echo └──────────────────────────────────────────────────────────────┘
echo.

REM Start the application
python simple_app.py

echo.
echo ┌──────────────────────────────────────────────────────────────┐
echo │  ✅ تم إيقاف النظام بنجاح                                    │
echo │  🙏 شكراً لاستخدام نظام إدارة الرواتب القطري               │
echo └──────────────────────────────────────────────────────────────┘
echo.
pause

{% extends "simple_base.html" %}

{% block title %}لوحة التحكم - نظام الرواتب{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</h1>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stats-card text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>{{ total_workers }}</h3>
                        <p class="mb-0">العمال النشطون</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>{{ total_payments_this_month }}</h3>
                        <p class="mb-0">مدفوعات هذا الشهر</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-money-bill-wave fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>{{ unpaid_payments }}</h3>
                        <p class="mb-0">رواتب غير مدفوعة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>{{ "%.0f"|format(total_amount_this_month) }}</h3>
                        <p class="mb-0">إجمالي المدفوع (ريال قطري)</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Payments -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-clock"></i> المدفوعات الأخيرة</h5>
            </div>
            <div class="card-body">
                {% if recent_payments %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>العامل</th>
                                    <th>الشهر</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payment in recent_payments %}
                                <tr>
                                    <td>
                                        <strong>{{ payment.worker.name }}</strong><br>
                                        <small class="text-muted">
                                            <i class="fas fa-flag"></i> {{ payment.worker.nationality }}
                                        </small>
                                    </td>
                                    <td>
                                        <strong>{{ payment.month }}</strong><br>
                                        <small class="text-muted">
                                            {% set month_num = payment.month.split('-')[1]|int %}
                                            {% if month_num == 1 %}يناير
                                            {% elif month_num == 2 %}فبراير
                                            {% elif month_num == 3 %}مارس
                                            {% elif month_num == 4 %}أبريل
                                            {% elif month_num == 5 %}مايو
                                            {% elif month_num == 6 %}يونيو
                                            {% elif month_num == 7 %}يوليو
                                            {% elif month_num == 8 %}أغسطس
                                            {% elif month_num == 9 %}سبتمبر
                                            {% elif month_num == 10 %}أكتوبر
                                            {% elif month_num == 11 %}نوفمبر
                                            {% elif month_num == 12 %}ديسمبر
                                            {% endif %} {{ payment.month.split('-')[0] }}
                                        </small>
                                    </td>
                                    <td>
                                        <strong>{{ "%.2f"|format(payment.total_amount) }} ر.ق</strong><br>
                                        <small class="text-muted">
                                            أساسي: {{ "%.0f"|format(payment.base_salary) }}
                                            {% if payment.bonuses > 0 %}+ {{ "%.0f"|format(payment.bonuses) }}{% endif %}
                                            {% if payment.deductions > 0 %}- {{ "%.0f"|format(payment.deductions) }}{% endif %}
                                        </small>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if payment.status == 'paid' else 'warning' if payment.status == 'partial' else 'danger' }}">
                                            {% if payment.status == 'paid' %}مدفوع
                                            {% elif payment.status == 'partial' %}جزئي
                                            {% else %}غير مدفوع
                                            {% endif %}
                                        </span>
                                        {% if payment.payment_date %}
                                        <br><small class="text-muted">{{ payment.payment_date.strftime('%d/%m') }}</small>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center">لا توجد مدفوعات حديثة.</p>
                {% endif %}
                <div class="text-end">
                    <a href="{{ url_for('salary_payments') }}" class="btn btn-sm btn-primary">عرض الكل</a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-bolt"></i> إجراءات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('add_worker') }}" class="btn btn-outline-primary">
                        <i class="fas fa-user-plus"></i> إضافة عامل
                    </a>
                    <a href="{{ url_for('add_salary_payment') }}" class="btn btn-outline-success">
                        <i class="fas fa-money-bill-wave"></i> إضافة راتب
                    </a>
                    <a href="{{ url_for('workers') }}" class="btn btn-outline-info">
                        <i class="fas fa-users"></i> عرض العمال
                    </a>
                    <a href="{{ url_for('salary_payments') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-list"></i> عرض الرواتب
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Section -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-flag"></i> إحصائيات الجنسيات</h5>
            </div>
            <div class="card-body">
                {% if nationality_stats %}
                    {% for nationality, count in nationality_stats %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span><i class="fas fa-flag text-primary"></i> {{ nationality }}</span>
                        <span class="badge bg-primary">{{ count }} عامل</span>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted text-center">لا توجد بيانات</p>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-briefcase"></i> إحصائيات أنواع العمل</h5>
            </div>
            <div class="card-body">
                {% if job_type_stats %}
                    {% for job_type, count in job_type_stats %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>
                            <i class="fas fa-briefcase text-success"></i>
                            {% if job_type == 'housemaid' %}خادمة منزل
                            {% elif job_type == 'driver' %}سائق
                            {% elif job_type == 'cook' %}طباخ
                            {% elif job_type == 'nanny' %}مربية أطفال
                            {% elif job_type == 'gardener' %}بستاني
                            {% elif job_type == 'cleaner' %}عامل نظافة
                            {% elif job_type == 'security_guard' %}حارس أمن
                            {% elif job_type == 'maintenance_worker' %}عامل صيانة
                            {% else %}{{ job_type }}
                            {% endif %}
                        </span>
                        <span class="badge bg-success">{{ count }} عامل</span>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted text-center">لا توجد بيانات</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

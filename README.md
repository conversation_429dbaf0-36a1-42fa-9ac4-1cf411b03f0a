# نظام إدارة رواتب العمالة المنزلية
## Domestic Workers Salary Management System

نظام شامل لإدارة رواتب العمالة المنزلية مطور بـ Python Flask مع دعم كامل للغة العربية.

## 🌟 المميزات الرئيسية

### ✅ إدارة العمال
- **إضافة عمال جدد** مع معلومات شاملة
- **قائمة شاملة بالجنسيات** من جميع أنحاء العالم
- **أنواع عمل متنوعة** تشمل جميع التخصصات المنزلية
- **رقم هوية مرن** يدعم الهوية الوطنية والإقامة وجواز السفر
- **بحث وتصفية** متقدمة

### ✅ إدارة الرواتب
- **حساب تلقائي للرواتب** مع الساعات الإضافية والمكافآت
- **تتبع حالة الدفع** (مدفوع/غير مدفوع/جزئي)
- **طرق دفع متعددة** (نقد/تحويل بنكي/شيك)
- **منع التكرار** لنفس العامل في نفس الشهر
- **ملاحظات مفصلة** لكل راتب

### ✅ لوحة التحكم
- **إحصائيات فورية** للعمال والرواتب
- **إحصائيات الجنسيات** وأنواع العمل
- **المعاملات الأخيرة** مع تفاصيل كاملة
- **تنبيهات الرواتب** غير المدفوعة

### ✅ واجهة المستخدم
- **دعم كامل للعربية** مع RTL
- **تصميم متجاوب** للهواتف والأجهزة اللوحية
- **رسائل تأكيد** واضحة ومفيدة
- **تحقق من صحة البيانات** شامل

## 🚀 التشغيل السريع

### المتطلبات
- Python 3.7+
- Flask
- SQLAlchemy

### التثبيت والتشغيل
```bash
# تشغيل التطبيق
python simple_app.py
```

التطبيق سيعمل على العنوان: **http://localhost:7474**

### تسجيل الدخول الافتراضي
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

## 📋 أنواع العمل المدعومة

### العمالة المنزلية
- خادمة منزل
- مدبرة منزل
- عامل/عاملة نظافة
- عامل/عاملة غسيل وكي
- مساعد منزلي عام

### رعاية الأطفال وكبار السن
- مربية أطفال
- جليسة أطفال
- مرافق كبار السن
- ممرض/ممرضة منزلية
- أخصائي علاج طبيعي

### الطبخ والمطبخ
- طباخ/طباخة
- شيف
- مساعد مطبخ
- شيف حلويات

### السائقين والنقل
- سائق شخصي
- سائق عائلة
- سائق توصيل
- سائق خاص

### الحدائق والمساحات الخارجية
- بستاني
- منسق حدائق
- عامل تنظيف مسابح
- صيانة المساحات الخارجية

### الأمن والحراسة
- حارس أمن
- بواب
- حارس ليلي

### الصيانة والإصلاح
- عامل صيانة عامة
- كهربائي
- سباك
- دهان
- نجار
- فني تكييف

### الخدمات المتخصصة
- مساعد شخصي
- مدرس خصوصي
- مترجم
- مختص رعاية حيوانات أليفة
- منظم فعاليات

### الخدمات التقنية
- دعم تقني
- فني منزل ذكي
- فني صوتيات ومرئيات

## 🌍 الجنسيات المدعومة

النظام يدعم أكثر من 80 جنسية مقسمة حسب القارات:
- **الدول العربية:** 22 دولة
- **آسيا:** 20 دولة
- **أفريقيا:** 16 دولة
- **أوروبا:** 10 دول
- **الأمريكتان:** 8 دول
- **أوقيانوسيا:** 2 دولة

## 🔧 المميزات التقنية

### الأمان
- تشفير كلمات المرور
- جلسات آمنة
- التحقق من صحة البيانات
- منع SQL Injection

### قاعدة البيانات
- SQLite للبساطة
- نماذج بيانات محسنة
- فهرسة للبحث السريع
- علاقات محسنة بين الجداول

### واجهة المستخدم
- Bootstrap 5 مع دعم RTL
- Font Awesome للأيقونات
- تصميم متجاوب
- تجربة مستخدم محسنة

## 📊 الإحصائيات والتقارير

- إجمالي العمال النشطين
- مدفوعات الشهر الحالي
- الرواتب غير المدفوعة
- إجمالي المبالغ المدفوعة
- توزيع العمال حسب الجنسية
- توزيع العمال حسب نوع العمل

## 🔮 التطوير المستقبلي

- تصدير التقارير PDF/Excel
- نظام إشعارات
- رفع الصور والمستندات
- تقارير مفصلة
- نسخ احتياطية تلقائية
- دعم عملات متعددة
- تطبيق موبايل

## 🌐 الوصول للنظام

بعد تشغيل التطبيق، يمكن الوصول إليه عبر:
- **العنوان المحلي:** http://localhost:7474
- **الشبكة المحلية:** http://[عنوان-الجهاز]:7474

## 📞 الدعم

للدعم والاستفسارات، يرجى التواصل معنا.

---

**تم تطوير النظام بعناية ليلبي احتياجات إدارة العمالة المنزلية في المملكة العربية السعودية ودول الخليج العربي.**

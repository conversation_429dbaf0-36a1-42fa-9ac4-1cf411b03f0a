{% extends "base.html" %}

{% block title %}View Payment - Salary System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-eye"></i> Salary Payment Details</h1>
            <div>
                <a href="{{ url_for('salary_receipt', id=payment.id) }}" class="btn btn-success me-2">
                    <i class="fas fa-download"></i> Download Receipt
                </a>
                <a href="{{ url_for('salary_payments') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Payments
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> Payment Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="40%">Worker:</th>
                                <td>{{ payment.worker.name }}</td>
                            </tr>
                            <tr>
                                <th>Job Type:</th>
                                <td>{{ payment.worker.job_type.replace('_', ' ').title() }}</td>
                            </tr>
                            <tr>
                                <th>Month:</th>
                                <td>{{ payment.month }}</td>
                            </tr>
                            <tr>
                                <th>Base Salary:</th>
                                <td>{{ "%.2f"|format(payment.base_salary) }} SAR</td>
                            </tr>
                            <tr>
                                <th>Overtime Hours:</th>
                                <td>{{ payment.overtime_hours }} hours</td>
                            </tr>
                            <tr>
                                <th>Overtime Rate:</th>
                                <td>{{ "%.2f"|format(payment.overtime_rate) }} SAR/hour</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="40%">Overtime Amount:</th>
                                <td>{{ "%.2f"|format(payment.overtime_hours * payment.overtime_rate) }} SAR</td>
                            </tr>
                            <tr>
                                <th>Bonuses:</th>
                                <td>{{ "%.2f"|format(payment.bonuses) }} SAR</td>
                            </tr>
                            <tr>
                                <th>Deductions:</th>
                                <td>{{ "%.2f"|format(payment.deductions) }} SAR</td>
                            </tr>
                            <tr>
                                <th>Vacation Days:</th>
                                <td>{{ payment.vacation_days }} days</td>
                            </tr>
                            <tr>
                                <th><strong>Total Amount:</strong></th>
                                <td><strong class="text-primary">{{ "%.2f"|format(payment.total_amount) }} SAR</strong></td>
                            </tr>
                            <tr>
                                <th>Status:</th>
                                <td>
                                    <span class="badge bg-{{ 'success' if payment.status == 'paid' else 'warning' if payment.status == 'partial' else 'danger' }}">
                                        {{ payment.status.title() }}
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        {% if payment.notes %}
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-sticky-note"></i> Notes</h5>
            </div>
            <div class="card-body">
                <p>{{ payment.notes }}</p>
            </div>
        </div>
        {% endif %}
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-credit-card"></i> Payment Details</h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <th>Payment Date:</th>
                        <td>
                            {% if payment.payment_date %}
                                {{ payment.payment_date.strftime('%Y-%m-%d') }}
                            {% else %}
                                <span class="text-muted">Not set</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th>Payment Method:</th>
                        <td>
                            {% if payment.payment_method %}
                                {{ payment.payment_method.replace('_', ' ').title() }}
                            {% else %}
                                <span class="text-muted">Not set</span>
                            {% endif %}
                        </td>
                    </tr>
                    <tr>
                        <th>Created:</th>
                        <td>{{ payment.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                    </tr>
                </table>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-user"></i> Worker Details</h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <th>Name:</th>
                        <td>{{ payment.worker.name }}</td>
                    </tr>
                    <tr>
                        <th>Nationality:</th>
                        <td>{{ payment.worker.nationality }}</td>
                    </tr>
                    <tr>
                        <th>ID Number:</th>
                        <td>{{ payment.worker.id_number }}</td>
                    </tr>
                    <tr>
                        <th>Employment Date:</th>
                        <td>{{ payment.worker.employment_date.strftime('%Y-%m-%d') }}</td>
                    </tr>
                </table>
                <div class="text-center">
                    <a href="{{ url_for('view_worker', id=payment.worker.id) }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-user"></i> View Worker
                    </a>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-tools"></i> Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('edit_salary_payment', id=payment.id) }}" class="btn btn-primary">
                        <i class="fas fa-edit"></i> Edit Payment
                    </a>
                    <a href="{{ url_for('salary_receipt', id=payment.id) }}" class="btn btn-success">
                        <i class="fas fa-download"></i> Download Receipt
                    </a>
                    <a href="{{ url_for('salary_receipt', id=payment.id, lang='ar') }}" class="btn btn-info">
                        <i class="fas fa-download"></i> Download Receipt (Arabic)
                    </a>
                    {% if current_user.is_admin() %}
                    <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                        <i class="fas fa-trash"></i> Delete Payment
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
{% if current_user.is_admin() %}
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this salary payment?</p>
                <p><strong>Worker:</strong> {{ payment.worker.name }}</p>
                <p><strong>Month:</strong> {{ payment.month }}</p>
                <p><strong>Amount:</strong> {{ "%.2f"|format(payment.total_amount) }} SAR</p>
                <p class="text-danger"><small>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST" action="{{ url_for('delete_salary_payment', id=payment.id) }}" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
{% if current_user.is_admin() %}
<script>
function confirmDelete() {
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endif %}
{% endblock %}

{% extends "simple_base.html" %}

{% block title %}إدارة الرواتب - نظام الرواتب{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-money-bill-wave"></i> إدارة الرواتب</h1>
            <a href="{{ url_for('add_salary_payment') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة راتب
            </a>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-3">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">الحالة</label>
                        <select name="status" class="form-select">
                            <option value="">جميع الحالات</option>
                            <option value="paid" {{ 'selected' if current_status == 'paid' }}>مدفوع</option>
                            <option value="unpaid" {{ 'selected' if current_status == 'unpaid' }}>غير مدفوع</option>
                            <option value="partial" {{ 'selected' if current_status == 'partial' }}>مدفوع جزئياً</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">العامل</label>
                        <select name="worker_id" class="form-select">
                            <option value="">جميع العمال</option>
                            {% for worker in workers %}
                            <option value="{{ worker.id }}" {{ 'selected' if current_worker_id == worker.id }}>
                                {{ worker.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-outline-primary me-2">
                            <i class="fas fa-filter"></i> تصفية
                        </button>
                        <a href="{{ url_for('salary_payments') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> مسح
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Payments Table -->
<div class="card">
    <div class="card-body">
        {% if payments %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>العامل</th>
                        <th>الشهر</th>
                        <th>الراتب الأساسي</th>
                        <th>الساعات الإضافية</th>
                        <th>المكافآت</th>
                        <th>الخصومات</th>
                        <th>إجمالي المبلغ</th>
                        <th>تاريخ الدفع</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for payment in payments %}
                    <tr>
                        <td>
                            <strong>{{ payment.worker.name }}</strong><br>
                            <small class="text-muted">
                                <i class="fas fa-flag"></i> {{ payment.worker.nationality }} -
                                {% if payment.worker.job_type == 'housemaid' %}خادمة منزل
                                {% elif payment.worker.job_type == 'driver' %}سائق
                                {% elif payment.worker.job_type == 'cook' %}طباخ
                                {% elif payment.worker.job_type == 'nanny' %}مربية أطفال
                                {% elif payment.worker.job_type == 'gardener' %}بستاني
                                {% elif payment.worker.job_type == 'cleaner' %}عامل نظافة
                                {% elif payment.worker.job_type == 'security_guard' %}حارس أمن
                                {% elif payment.worker.job_type == 'maintenance_worker' %}عامل صيانة
                                {% else %}{{ payment.worker.job_type }}
                                {% endif %}
                            </small>
                        </td>
                        <td>{{ payment.month }}</td>
                        <td>{{ "%.2f"|format(payment.base_salary) }} ر.ق</td>
                        <td>
                            {% if payment.overtime_hours > 0 %}
                                {{ payment.overtime_hours }} ساعة<br>
                                <small class="text-muted">{{ "%.2f"|format(payment.overtime_hours * payment.overtime_rate) }} ر.ق</small>
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            {% if payment.bonuses > 0 %}
                                {{ "%.2f"|format(payment.bonuses) }} ر.ق
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            {% if payment.deductions > 0 %}
                                {{ "%.2f"|format(payment.deductions) }} ر.ق
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            <strong>{{ "%.2f"|format(payment.total_amount) }} ر.ق</strong>
                        </td>
                        <td>
                            {% if payment.payment_date %}
                                {{ payment.payment_date.strftime('%Y-%m-%d') }}<br>
                                <small class="text-muted">{{ payment.payment_method or 'غير محدد' }}</small>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-{{ 'success' if payment.status == 'paid' else 'warning' if payment.status == 'partial' else 'danger' }}">
                                {{ 'مدفوع' if payment.status == 'paid' else 'جزئي' if payment.status == 'partial' else 'غير مدفوع' }}
                            </span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('edit_salary_payment', id=payment.id) }}"
                                   class="btn btn-outline-primary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form method="POST" action="{{ url_for('toggle_payment_status', id=payment.id) }}" style="display: inline;">
                                    <button type="submit" class="btn btn-outline-{{ 'warning' if payment.status == 'paid' else 'success' }}"
                                            title="{{ 'تغيير إلى غير مدفوع' if payment.status == 'paid' else 'تغيير إلى مدفوع' }}">
                                        <i class="fas fa-{{ 'times' if payment.status == 'paid' else 'check' }}"></i>
                                    </button>
                                </form>
                                {% if current_user.is_admin() %}
                                <button type="button" class="btn btn-outline-danger"
                                        onclick="confirmDelete({{ payment.id }}, '{{ payment.worker.name }}', '{{ payment.month }}')"
                                        title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">لا توجد رواتب</h4>
            <p class="text-muted">ابدأ بإضافة رواتب للعمال.</p>
            <a href="{{ url_for('add_salary_payment') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> إضافة راتب
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف راتب العامل <strong id="workerName"></strong> لشهر <strong id="paymentMonth"></strong>؟</p>
                <p class="text-danger"><small>هذا الإجراء لا يمكن التراجع عنه.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(paymentId, workerName, paymentMonth) {
    document.getElementById('workerName').textContent = workerName;
    document.getElementById('paymentMonth').textContent = paymentMonth;
    document.getElementById('deleteForm').action = '/salary-payments/' + paymentId + '/delete';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}

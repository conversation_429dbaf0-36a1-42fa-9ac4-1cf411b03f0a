{% extends "simple_base.html" %}

{% block title %}الإعدادات - نظام الرواتب{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-cogs"></i> إعدادات النظام</h1>
            <div class="badge bg-danger fs-6">
                <i class="fas fa-shield-alt"></i> مدير النظام فقط
            </div>
        </div>
    </div>
</div>

<!-- Settings Categories -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5><i class="fas fa-server"></i> إعدادات النظام</h5>
            </div>
            <div class="card-body">
                <p>إدارة قاعدة البيانات والنسخ الاحتياطية وتحسين الأداء</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success"></i> النسخ الاحتياطية</li>
                    <li><i class="fas fa-check text-success"></i> تحسين قاعدة البيانات</li>
                    <li><i class="fas fa-check text-success"></i> مسح السجلات</li>
                    <li><i class="fas fa-check text-success"></i> معلومات النظام</li>
                </ul>
            </div>
            <div class="card-footer">
                <a href="{{ url_for('system_settings') }}" class="btn btn-primary w-100">
                    <i class="fas fa-server"></i> إعدادات النظام
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card h-100">
            <div class="card-header bg-danger text-white">
                <h5><i class="fas fa-shield-alt"></i> إعدادات الأمان</h5>
            </div>
            <div class="card-body">
                <p>إدارة أمان النظام والمستخدمين والجلسات</p>
                <ul class="list-unstyled">
                    <li><i class="fas fa-check text-success"></i> إدارة الجلسات</li>
                    <li><i class="fas fa-check text-success"></i> المستخدمين غير النشطين</li>
                    <li><i class="fas fa-check text-success"></i> سياسة كلمات المرور</li>
                    <li><i class="fas fa-check text-success"></i> سجل الدخول</li>
                </ul>
            </div>
            <div class="card-footer">
                <a href="{{ url_for('security_settings') }}" class="btn btn-danger w-100">
                    <i class="fas fa-shield-alt"></i> إعدادات الأمان
                </a>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card h-100">
            <div class="card-header bg-info text-white">
                <h5><i class="fas fa-chart-bar"></i> إحصائيات النظام</h5>
            </div>
            <div class="card-body">
                <p>عرض إحصائيات شاملة عن استخدام النظام</p>
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-primary">{{ stats.total_users }}</h4>
                        <small>إجمالي المستخدمين</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-success">{{ stats.total_workers }}</h4>
                        <small>إجمالي العمال</small>
                    </div>
                    <div class="col-6 mt-2">
                        <h4 class="text-warning">{{ stats.total_payments }}</h4>
                        <small>إجمالي الرواتب</small>
                    </div>
                    <div class="col-6 mt-2">
                        <h4 class="text-info">{{ stats.database_size }}</h4>
                        <small>حجم قاعدة البيانات (MB)</small>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{{ url_for('reports') }}" class="btn btn-info w-100">
                    <i class="fas fa-chart-bar"></i> التقارير التفصيلية
                </a>
            </div>
        </div>
    </div>
</div>

<!-- System Overview -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-tachometer-alt"></i> نظرة عامة على النظام</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h4>{{ stats.total_users }}</h4>
                                <small>إجمالي المستخدمين</small>
                                <hr class="my-2">
                                <div class="d-flex justify-content-between">
                                    <span>نشط: {{ stats.active_users }}</span>
                                    <span>مدير: {{ stats.admin_users }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-user-tie fa-2x mb-2"></i>
                                <h4>{{ stats.total_workers }}</h4>
                                <small>إجمالي العمال</small>
                                <hr class="my-2">
                                <div class="d-flex justify-content-between">
                                    <span>نشط: {{ stats.active_workers }}</span>
                                    <span>غير نشط: {{ stats.total_workers - stats.active_workers }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                                <h4>{{ stats.total_payments }}</h4>
                                <small>إجمالي الرواتب</small>
                                <hr class="my-2">
                                <div class="d-flex justify-content-between">
                                    <span>مدفوع: {{ stats.paid_payments }}</span>
                                    <span>معلق: {{ stats.unpaid_payments }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-database fa-2x mb-2"></i>
                                <h4>{{ stats.database_size }}</h4>
                                <small>حجم قاعدة البيانات (MB)</small>
                                <hr class="my-2">
                                <div class="text-center">
                                    {% if stats.last_backup %}
                                        <small>آخر نسخة: {{ stats.last_backup.strftime('%Y-%m-%d') }}</small>
                                    {% else %}
                                        <small>لا توجد نسخ احتياطية</small>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-bolt"></i> إجراءات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('system_settings') }}" class="btn btn-outline-primary">
                        <i class="fas fa-database"></i> نسخة احتياطية
                    </a>
                    <a href="{{ url_for('security_settings') }}" class="btn btn-outline-danger">
                        <i class="fas fa-shield-alt"></i> إعدادات الأمان
                    </a>
                    <a href="{{ url_for('users') }}" class="btn btn-outline-warning">
                        <i class="fas fa-user-cog"></i> إدارة المستخدمين
                    </a>
                    <a href="{{ url_for('reports') }}" class="btn btn-outline-info">
                        <i class="fas fa-chart-bar"></i> التقارير
                    </a>
                </div>
            </div>
        </div>
        
        <!-- System Status -->
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-heartbeat"></i> حالة النظام</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>قاعدة البيانات</span>
                    <span class="badge bg-success">متصلة</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>الخادم</span>
                    <span class="badge bg-success">يعمل</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>النسخ الاحتياطية</span>
                    {% if stats.last_backup %}
                        <span class="badge bg-success">محدثة</span>
                    {% else %}
                        <span class="badge bg-warning">مطلوبة</span>
                    {% endif %}
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>الأمان</span>
                    <span class="badge bg-success">آمن</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-clock"></i> النشاطات الأخيرة في النظام</h5>
            </div>
            <div class="card-body">
                {% if recent_activities %}
                <div class="timeline">
                    {% for activity in recent_activities %}
                    <div class="d-flex mb-3">
                        <div class="flex-shrink-0">
                            <div class="bg-{{ activity.color }} text-white rounded-circle d-flex align-items-center justify-content-center" 
                                 style="width: 40px; height: 40px;">
                                <i class="{{ activity.icon }}"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="d-flex justify-content-between">
                                <h6 class="mb-1">{{ activity.description }}</h6>
                                <small class="text-muted">{{ activity.date.strftime('%Y-%m-%d %H:%M') }}</small>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد نشاطات حديثة</h5>
                    <p class="text-muted">سيتم عرض النشاطات الأخيرة في النظام هنا.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.timeline {
    max-height: 400px;
    overflow-y: auto;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.btn:hover {
    transform: translateY(-1px);
    transition: all 0.2s;
}

.badge {
    font-size: 0.875em;
}
</style>
{% endblock %}

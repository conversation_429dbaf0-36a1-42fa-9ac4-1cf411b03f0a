{% extends "simple_base.html" %}

{% block title %}الملف الشخصي - نظام الرواتب{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-user-circle"></i> الملف الشخصي</h1>
            <a href="{{ url_for('edit_profile') }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> تعديل الملف الشخصي
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- User Information -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-user"></i> معلومات المستخدم</h5>
            </div>
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fas fa-user-circle fa-5x text-primary"></i>
                </div>
                <h4>{{ current_user.username }}</h4>
                <p class="text-muted">{{ current_user.email }}</p>
                <span class="badge bg-{{ 'danger' if current_user.role == 'admin' else 'primary' }} fs-6">
                    {{ 'مدير النظام' if current_user.role == 'admin' else 'مستخدم عادي' }}
                </span>
                <hr>
                <div class="row text-center">
                    <div class="col-6">
                        <h6 class="text-muted">تاريخ الإنشاء</h6>
                        <p>{{ current_user.created_at.strftime('%Y-%m-%d') }}</p>
                    </div>
                    <div class="col-6">
                        <h6 class="text-muted">الحالة</h6>
                        <span class="badge bg-{{ 'success' if current_user.is_active else 'secondary' }}">
                            {{ 'نشط' if current_user.is_active else 'غير نشط' }}
                        </span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-bolt"></i> إجراءات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('add_worker') }}" class="btn btn-outline-primary">
                        <i class="fas fa-user-plus"></i> إضافة عامل
                    </a>
                    <a href="{{ url_for('add_salary_payment') }}" class="btn btn-outline-success">
                        <i class="fas fa-money-bill-wave"></i> إضافة راتب
                    </a>
                    <a href="{{ url_for('reports') }}" class="btn btn-outline-info">
                        <i class="fas fa-chart-bar"></i> التقارير
                    </a>
                    {% if current_user.is_admin() %}
                    <a href="{{ url_for('users') }}" class="btn btn-outline-warning">
                        <i class="fas fa-user-cog"></i> إدارة المستخدمين
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Statistics -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-pie"></i> الإحصائيات</h5>
            </div>
            <div class="card-body">
                {% if current_user.is_admin() %}
                <!-- Admin Statistics -->
                <div class="row">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h4>{{ stats.total_users }}</h4>
                                <small>إجمالي المستخدمين</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-user-check fa-2x mb-2"></i>
                                <h4>{{ stats.active_users }}</h4>
                                <small>المستخدمين النشطين</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-user-tie fa-2x mb-2"></i>
                                <h4>{{ stats.total_workers }}</h4>
                                <small>إجمالي العمال</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                                <h4>{{ stats.total_payments }}</h4>
                                <small>إجمالي الرواتب</small>
                            </div>
                        </div>
                    </div>
                </div>
                {% else %}
                <!-- Regular User Statistics -->
                <div class="row">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h4>{{ stats.total_workers }}</h4>
                                <small>إجمالي العمال</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-user-check fa-2x mb-2"></i>
                                <h4>{{ stats.active_workers }}</h4>
                                <small>العمال النشطين</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-list fa-2x mb-2"></i>
                                <h4>{{ stats.total_payments }}</h4>
                                <small>إجمالي الرواتب</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-check fa-2x mb-2"></i>
                                <h4>{{ stats.paid_payments }}</h4>
                                <small>مدفوع</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card bg-danger text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-times fa-2x mb-2"></i>
                                <h4>{{ stats.unpaid_payments }}</h4>
                                <small>غير مدفوع</small>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Recent Activities -->
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-clock"></i> النشاطات الأخيرة</h5>
            </div>
            <div class="card-body">
                {% if stats.recent_activities %}
                <div class="timeline">
                    {% for activity in stats.recent_activities %}
                    <div class="d-flex mb-3">
                        <div class="flex-shrink-0">
                            <div class="bg-{{ activity.color }} text-white rounded-circle d-flex align-items-center justify-content-center" 
                                 style="width: 40px; height: 40px;">
                                <i class="{{ activity.icon }}"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <div class="d-flex justify-content-between">
                                <h6 class="mb-1">{{ activity.description }}</h6>
                                <small class="text-muted">{{ activity.date.strftime('%Y-%m-%d %H:%M') }}</small>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد نشاطات حديثة</h5>
                    <p class="text-muted">ابدأ بإضافة عمال أو رواتب لرؤية النشاطات هنا.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.timeline {
    max-height: 400px;
    overflow-y: auto;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.badge {
    font-size: 0.875em;
}

.btn-outline-primary:hover,
.btn-outline-success:hover,
.btn-outline-info:hover,
.btn-outline-warning:hover {
    transform: translateY(-1px);
    transition: all 0.2s;
}
</style>
{% endblock %}

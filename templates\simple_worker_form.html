{% extends "simple_base.html" %}

{% block title %}{{ title }} - نظام الرواتب{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-user-plus"></i> {{ title }}</h1>
            <a href="{{ url_for('workers') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> العودة للعمال
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <h5 class="mb-3"><i class="fas fa-user"></i> المعلومات الأساسية</h5>
                            
                            <div class="mb-3">
                                <label class="form-label">الاسم</label>
                                <input type="text" name="name" class="form-control" required
                                       value="{{ worker.name if worker else '' }}">
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">الجنسية</label>
                                <select name="nationality" class="form-select" required>
                                    <option value="">اختر الجنسية</option>
                                    <optgroup label="الدول العربية">
                                        <option value="قطر" {{ 'selected' if (worker and worker.nationality == 'قطر') or not worker else '' }}>قطر 🇶🇦</option>
                                        <option value="السعودية" {{ 'selected' if worker and worker.nationality == 'السعودية' else '' }}>السعودية</option>
                                        <option value="الإمارات" {{ 'selected' if worker and worker.nationality == 'الإمارات' else '' }}>الإمارات العربية المتحدة</option>
                                        <option value="الكويت" {{ 'selected' if worker and worker.nationality == 'الكويت' else '' }}>الكويت</option>
                                        <option value="البحرين" {{ 'selected' if worker and worker.nationality == 'البحرين' else '' }}>البحرين</option>
                                        <option value="عمان" {{ 'selected' if worker and worker.nationality == 'عمان' else '' }}>عمان</option>
                                        <option value="الأردن">الأردن</option>
                                        <option value="لبنان">لبنان</option>
                                        <option value="سوريا">سوريا</option>
                                        <option value="العراق">العراق</option>
                                        <option value="فلسطين">فلسطين</option>
                                        <option value="مصر">مصر</option>
                                        <option value="ليبيا">ليبيا</option>
                                        <option value="تونس">تونس</option>
                                        <option value="الجزائر">الجزائر</option>
                                        <option value="المغرب">المغرب</option>
                                        <option value="السودان">السودان</option>
                                        <option value="الصومال">الصومال</option>
                                        <option value="جيبوتي">جيبوتي</option>
                                        <option value="موريتانيا">موريتانيا</option>
                                        <option value="جزر القمر">جزر القمر</option>
                                        <option value="اليمن">اليمن</option>
                                    </optgroup>
                                    <optgroup label="آسيا">
                                        <option value="الفلبين">الفلبين</option>
                                        <option value="إندونيسيا">إندونيسيا</option>
                                        <option value="الهند">الهند</option>
                                        <option value="باكستان">باكستان</option>
                                        <option value="بنغلاديش">بنغلاديش</option>
                                        <option value="سريلانكا">سريلانكا</option>
                                        <option value="نيبال">نيبال</option>
                                        <option value="ميانمار">ميانمار</option>
                                        <option value="تايلاند">تايلاند</option>
                                        <option value="فيتنام">فيتنام</option>
                                        <option value="كمبوديا">كمبوديا</option>
                                        <option value="ماليزيا">ماليزيا</option>
                                        <option value="سنغافورة">سنغافورة</option>
                                        <option value="الصين">الصين</option>
                                        <option value="اليابان">اليابان</option>
                                        <option value="كوريا الجنوبية">كوريا الجنوبية</option>
                                        <option value="كوريا الشمالية">كوريا الشمالية</option>
                                        <option value="إيران">إيران</option>
                                        <option value="أفغانستان">أفغانستان</option>
                                        <option value="تركيا">تركيا</option>
                                    </optgroup>
                                    <optgroup label="أفريقيا">
                                        <option value="إثيوبيا">إثيوبيا</option>
                                        <option value="إريتريا">إريتريا</option>
                                        <option value="كينيا">كينيا</option>
                                        <option value="أوغندا">أوغندا</option>
                                        <option value="تنزانيا">تنزانيا</option>
                                        <option value="رواندا">رواندا</option>
                                        <option value="بوروندي">بوروندي</option>
                                        <option value="نيجيريا">نيجيريا</option>
                                        <option value="غانا">غانا</option>
                                        <option value="السنغال">السنغال</option>
                                        <option value="مالي">مالي</option>
                                        <option value="بوركينا فاسو">بوركينا فاسو</option>
                                        <option value="النيجر">النيجر</option>
                                        <option value="تشاد">تشاد</option>
                                        <option value="الكاميرون">الكاميرون</option>
                                        <option value="جنوب أفريقيا">جنوب أفريقيا</option>
                                    </optgroup>
                                    <optgroup label="أوروبا">
                                        <option value="ألمانيا">ألمانيا</option>
                                        <option value="فرنسا">فرنسا</option>
                                        <option value="إيطاليا">إيطاليا</option>
                                        <option value="إسبانيا">إسبانيا</option>
                                        <option value="البرتغال">البرتغال</option>
                                        <option value="بريطانيا">بريطانيا</option>
                                        <option value="روسيا">روسيا</option>
                                        <option value="أوكرانيا">أوكرانيا</option>
                                        <option value="بولندا">بولندا</option>
                                        <option value="رومانيا">رومانيا</option>
                                    </optgroup>
                                    <optgroup label="الأمريكتان">
                                        <option value="الولايات المتحدة">الولايات المتحدة</option>
                                        <option value="كندا">كندا</option>
                                        <option value="المكسيك">المكسيك</option>
                                        <option value="البرازيل">البرازيل</option>
                                        <option value="الأرجنتين">الأرجنتين</option>
                                        <option value="كولومبيا">كولومبيا</option>
                                        <option value="فنزويلا">فنزويلا</option>
                                        <option value="بيرو">بيرو</option>
                                    </optgroup>
                                    <optgroup label="أوقيانوسيا">
                                        <option value="أستراليا">أستراليا</option>
                                        <option value="نيوزيلندا">نيوزيلندا</option>
                                    </optgroup>
                                    <option value="أخرى">أخرى</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">رقم الهوية/الإقامة</label>
                                <input type="text" name="id_number" class="form-control" required
                                       placeholder="مثال: 12345678901 (قطري) أو 2345678901 (مقيم)"
                                       minlength="8" maxlength="15"
                                       title="يجب أن يكون الرقم بين 8-15 رقم"
                                       value="{{ worker.id_number if worker else '' }}">
                                <div class="form-text">
                                    <small>
                                        <i class="fas fa-info-circle"></i>
                                        أدخل رقم الهوية القطرية أو رقم الإقامة أو جواز السفر
                                        <br>
                                        <strong>أمثلة للوثائق القطرية:</strong>
                                        <ul class="mb-0 mt-1">
                                            <li>هوية قطرية: 12345678901 (11 رقم)</li>
                                            <li>إقامة قطر: 2345678901 (10 أرقام)</li>
                                            <li>جواز سفر: A12345678 (حروف وأرقام)</li>
                                            <li>رخصة عمل: W12345678 (حروف وأرقام)</li>
                                        </ul>
                                    </small>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">الجنس</label>
                                <select name="gender" class="form-select" required>
                                    <option value="">اختر الجنس</option>
                                    <option value="ذكر" {{ 'selected' if worker and worker.gender == 'ذكر' else '' }}>ذكر</option>
                                    <option value="أنثى" {{ 'selected' if worker and worker.gender == 'أنثى' else '' }}>أنثى</option>
                                </select>
                                <div class="form-text">
                                    <small>
                                        <i class="fas fa-venus-mars"></i>
                                        اختر جنس العامل (ذكر أو أنثى)
                                    </small>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">نوع العمل/الوظيفة</label>
                                <select name="job_type" class="form-select" required>
                                    <option value="">اختر نوع العمل</option>
                                    <optgroup label="الأكثر شيوعاً في قطر 🇶🇦">
                                        <option value="housemaid">خادمة منزل</option>
                                        <option value="driver">سائق شخصي</option>
                                        <option value="nanny">مربية أطفال</option>
                                        <option value="cook">طباخ/طباخة</option>
                                        <option value="cleaner">عامل/عاملة نظافة</option>
                                        <option value="gardener">بستاني</option>
                                        <option value="security_guard">حارس أمن</option>
                                    </optgroup>
                                    <optgroup label="العمالة المنزلية">
                                        <option value="housemaid">خادمة منزل</option>
                                        <option value="housekeeper">مدبرة منزل</option>
                                        <option value="cleaner">عامل/عاملة نظافة</option>
                                        <option value="laundry_worker">عامل/عاملة غسيل وكي</option>
                                        <option value="general_helper">مساعد منزلي عام</option>
                                    </optgroup>
                                    <optgroup label="رعاية الأطفال وكبار السن">
                                        <option value="nanny">مربية أطفال</option>
                                        <option value="babysitter">جليسة أطفال</option>
                                        <option value="elderly_care">مرافق كبار السن</option>
                                        <option value="nurse">ممرض/ممرضة منزلية</option>
                                        <option value="physiotherapist">أخصائي علاج طبيعي</option>
                                    </optgroup>
                                    <optgroup label="الطبخ والمطبخ">
                                        <option value="cook">طباخ/طباخة</option>
                                        <option value="chef">شيف</option>
                                        <option value="kitchen_helper">مساعد مطبخ</option>
                                        <option value="pastry_chef">شيف حلويات</option>
                                    </optgroup>
                                    <optgroup label="السائقين والنقل">
                                        <option value="driver">سائق شخصي</option>
                                        <option value="family_driver">سائق عائلة</option>
                                        <option value="delivery_driver">سائق توصيل</option>
                                        <option value="chauffeur">سائق خاص</option>
                                    </optgroup>
                                    <optgroup label="الحدائق والمساحات الخارجية">
                                        <option value="gardener">بستاني</option>
                                        <option value="landscaper">منسق حدائق</option>
                                        <option value="pool_cleaner">عامل تنظيف مسابح</option>
                                        <option value="outdoor_maintenance">صيانة المساحات الخارجية</option>
                                    </optgroup>
                                    <optgroup label="الأمن والحراسة">
                                        <option value="security_guard">حارس أمن</option>
                                        <option value="gate_keeper">بواب</option>
                                        <option value="night_guard">حارس ليلي</option>
                                    </optgroup>
                                    <optgroup label="الصيانة والإصلاح">
                                        <option value="maintenance_worker">عامل صيانة عامة</option>
                                        <option value="electrician">كهربائي</option>
                                        <option value="plumber">سباك</option>
                                        <option value="painter">دهان</option>
                                        <option value="carpenter">نجار</option>
                                        <option value="ac_technician">فني تكييف</option>
                                    </optgroup>
                                    <optgroup label="الخدمات المتخصصة">
                                        <option value="personal_assistant">مساعد شخصي</option>
                                        <option value="tutor">مدرس خصوصي</option>
                                        <option value="translator">مترجم</option>
                                        <option value="pet_care">مختص رعاية حيوانات أليفة</option>
                                        <option value="event_organizer">منظم فعاليات</option>
                                    </optgroup>
                                    <optgroup label="الخدمات التقنية">
                                        <option value="it_support">دعم تقني</option>
                                        <option value="smart_home_tech">فني منزل ذكي</option>
                                        <option value="av_technician">فني صوتيات ومرئيات</option>
                                    </optgroup>
                                    <optgroup label="أخرى">
                                        <option value="receptionist">موظف استقبال</option>
                                        <option value="office_assistant">مساعد مكتبي</option>
                                        <option value="other">أخرى</option>
                                    </optgroup>
                                </select>
                                <div class="form-text">
                                    <small>
                                        <i class="fas fa-briefcase"></i>
                                        اختر نوع العمل الذي يقوم به العامل في المنزل أو المؤسسة
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Employment Details -->
                        <div class="col-md-6">
                            <h5 class="mb-3"><i class="fas fa-briefcase"></i> تفاصيل التوظيف</h5>
                            
                            <div class="mb-3">
                                <label class="form-label">تاريخ التوظيف</label>
                                <input type="date" name="employment_date" class="form-control" required
                                       value="{{ worker.employment_date.strftime('%Y-%m-%d') if worker and worker.employment_date else '' }}">
                            </div>

                            <div class="mb-3">
                                <label class="form-label">الراتب الشهري</label>
                                <div class="input-group">
                                    <input type="number" name="monthly_salary" class="form-control" step="0.01" min="0" required
                                           value="{{ worker.monthly_salary if worker else '' }}">
                                    <span class="input-group-text">ر.ق</span>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input type="checkbox" name="is_active" class="form-check-input"
                                           {{ 'checked' if not worker or worker.is_active else '' }}>
                                    <label class="form-check-label">نشط</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <!-- Form Actions -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="{{ url_for('workers') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> {{ 'تحديث' if worker else 'حفظ' }} العامل
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Set today's date as default for employment date
document.addEventListener('DOMContentLoaded', function() {
    const employmentDateField = document.querySelector('input[name="employment_date"]');
    if (!employmentDateField.value) {
        employmentDateField.value = new Date().toISOString().split('T')[0];
    }

    // Set selected job type if editing
    {% if worker and worker.job_type %}
    const jobTypeSelect = document.querySelector('select[name="job_type"]');
    const jobTypeValue = '{{ worker.job_type }}';
    for (let option of jobTypeSelect.options) {
        if (option.value === jobTypeValue) {
            option.selected = true;
            break;
        }
    }
    {% endif %}

    // Set selected nationality if editing (for non-Qatar nationalities)
    {% if worker and worker.nationality and worker.nationality != 'قطر' %}
    const nationalitySelect = document.querySelector('select[name="nationality"]');
    const nationalityValue = '{{ worker.nationality }}';
    for (let option of nationalitySelect.options) {
        if (option.value === nationalityValue) {
            option.selected = true;
            break;
        }
    }
    {% endif %}
});
</script>
{% endblock %}

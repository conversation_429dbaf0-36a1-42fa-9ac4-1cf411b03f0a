#!/bin/bash

clear
echo ""
echo "    ╔═══════════════════════════════════════════════════════════╗"
echo "    ║  🇶🇦        نظام إدارة الرواتب القطري        🇶🇦  ║"
echo "    ║           Qatar Salary Management System              ║"
echo "    ║                   النسخة المُصححة                    ║"
echo "    ╚═══════════════════════════════════════════════════════════╝"
echo ""
echo "                🚀 تشغيل مباشر - بدون أخطاء 🚀"
echo ""
echo "    ✅ تم إصلاح جميع الأخطاء"
echo "    ✅ حقل الجنس يعمل بشكل مثالي"
echo "    ✅ أيقونات قطرية جميلة"
echo "    ✅ واجهة عربية متطورة"
echo ""

# Find Python
if command -v python3 &> /dev/null; then
    PYTHON="python3"
elif command -v python &> /dev/null; then
    PYTHON="python"
else
    echo "    ❌ Python غير موجود - يرجى تثبيته"
    echo "    📥 Ubuntu/Debian: sudo apt install python3 python3-pip"
    echo "    📥 CentOS/RHEL: sudo yum install python3 python3-pip"
    echo "    📥 macOS: brew install python3"
    exit 1
fi

echo "    ✅ Python موجود ($PYTHON)"

# Check and install requirements
$PYTHON -c "import flask, flask_sqlalchemy, werkzeug" &> /dev/null
if [ $? -ne 0 ]; then
    echo "    📦 تثبيت المكتبات..."
    $PYTHON -m pip install flask flask-sqlalchemy werkzeug &> /dev/null
    if [ $? -ne 0 ]; then
        echo "    ❌ فشل في تثبيت المكتبات"
        echo "    🌐 تأكد من الاتصال بالإنترنت"
        exit 1
    fi
    echo "    ✅ تم تثبيت المكتبات"
else
    echo "    ✅ جميع المكتبات موجودة"
fi

echo ""
echo "    🧪 اختبار التطبيق..."
$PYTHON -c "import simple_app; print('    ✅ التطبيق جاهز للتشغيل')" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "    ❌ خطأ في التطبيق"
    echo "    📝 تفاصيل الخطأ:"
    $PYTHON -c "import simple_app"
    exit 1
fi

echo ""
echo "    🚀 جاري تشغيل النظام..."
echo ""
echo "    🌐 العنوان: http://localhost:7474"
echo "    👤 المستخدم: admin"
echo "    🔑 كلمة المرور: admin123"
echo ""

# Open browser
if command -v xdg-open &> /dev/null; then
    xdg-open http://localhost:7474 &
elif command -v open &> /dev/null; then
    open http://localhost:7474 &
fi

# Start application
$PYTHON simple_app.py

echo ""
echo "    ✅ تم إيقاف النظام"

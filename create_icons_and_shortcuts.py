#!/usr/bin/env python3
"""
Create icons and shortcuts for the portable Qatar Salary System
"""

import os
import base64
from PIL import Image, ImageDraw, ImageFont
import io

def create_system_icon():
    """Create a beautiful icon for the Qatar Salary System"""
    
    # Create a 256x256 icon
    size = 256
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Qatar flag colors
    maroon = (126, 20, 35)  # Qatar maroon
    white = (255, 255, 255)
    
    # Draw background circle with gradient effect
    for i in range(size//2):
        alpha = int(255 * (1 - i/(size//2)))
        color = (*maroon, alpha)
        draw.ellipse([i, i, size-i, size-i], fill=color)
    
    # Draw main circle
    margin = 20
    draw.ellipse([margin, margin, size-margin, size-margin], 
                fill=maroon, outline=white, width=4)
    
    # Draw Qatar flag pattern (zigzag)
    points = []
    zigzag_width = 30
    for y in range(margin + 20, size - margin - 20, 15):
        if len(points) % 2 == 0:
            points.extend([(margin + 40, y), (margin + 40 + zigzag_width, y + 7)])
        else:
            points.extend([(margin + 40 + zigzag_width, y), (margin + 40, y + 7)])
    
    if len(points) >= 4:
        draw.polygon(points, fill=white)
    
    # Draw money/salary symbol
    center_x, center_y = size//2, size//2
    
    # Draw dollar/riyal symbol
    symbol_size = 60
    draw.text((center_x - symbol_size//2, center_y - symbol_size//2), 
              "ر.ق", fill=white, 
              font=None)  # Will use default font
    
    # Draw small decorative elements
    for angle in [0, 90, 180, 270]:
        x = center_x + 70 * (1 if angle in [0, 90] else -1)
        y = center_y + 70 * (1 if angle in [0, 270] else -1)
        draw.ellipse([x-5, y-5, x+5, y+5], fill=white)
    
    return img

def create_simple_icon():
    """Create a simple but effective icon"""
    
    size = 256
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Colors
    bg_color = (41, 128, 185)  # Nice blue
    accent_color = (231, 76, 60)  # Red accent
    white = (255, 255, 255)
    
    # Draw main background
    draw.ellipse([10, 10, size-10, size-10], fill=bg_color)
    
    # Draw border
    draw.ellipse([10, 10, size-10, size-10], outline=white, width=6)
    
    # Draw document/paper icon
    paper_left = 60
    paper_top = 50
    paper_right = size - 60
    paper_bottom = size - 50
    
    # Paper background
    draw.rectangle([paper_left, paper_top, paper_right, paper_bottom], 
                  fill=white, outline=accent_color, width=3)
    
    # Draw lines representing text/data
    line_spacing = 20
    line_margin = 20
    for i in range(6):
        y = paper_top + 30 + (i * line_spacing)
        if y < paper_bottom - 20:
            line_width = paper_right - paper_left - (line_margin * 2)
            if i % 3 == 2:  # Make some lines shorter
                line_width = line_width * 0.7
            
            draw.rectangle([paper_left + line_margin, y, 
                          paper_left + line_margin + line_width, y + 3], 
                         fill=bg_color)
    
    # Draw Qatar symbol (crescent)
    crescent_center_x = size - 40
    crescent_center_y = 40
    draw.ellipse([crescent_center_x - 15, crescent_center_y - 15,
                 crescent_center_x + 15, crescent_center_y + 15], 
                fill=accent_color)
    draw.ellipse([crescent_center_x - 10, crescent_center_y - 10,
                 crescent_center_x + 20, crescent_center_y + 20], 
                fill=bg_color)
    
    return img

def save_icon_formats(img, base_name):
    """Save icon in multiple formats"""
    
    # Save as PNG
    png_path = f"{base_name}.png"
    img.save(png_path, "PNG")
    print(f"   ✅ تم إنشاء: {png_path}")
    
    # Save as ICO (Windows icon)
    ico_path = f"{base_name}.ico"
    # Create multiple sizes for ICO
    sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)]
    ico_images = []
    
    for size in sizes:
        resized = img.resize(size, Image.Resampling.LANCZOS)
        ico_images.append(resized)
    
    ico_images[0].save(ico_path, format='ICO', sizes=[(img.width, img.height) for img in ico_images])
    print(f"   ✅ تم إنشاء: {ico_path}")
    
    return png_path, ico_path

def create_desktop_shortcut():
    """Create desktop shortcut content"""
    
    shortcut_content = """[Desktop Entry]
Version=1.0
Type=Application
Name=نظام إدارة الرواتب القطري
Name[en]=Qatar Salary Management System
Comment=نظام شامل لإدارة رواتب العمال في قطر
Comment[en]=Comprehensive salary management system for workers in Qatar
Exec=python3 simple_app.py
Icon=qatar_salary_icon.png
Terminal=false
Categories=Office;Finance;
StartupNotify=true
"""
    
    return shortcut_content

def create_windows_shortcut():
    """Create Windows shortcut (.lnk) using VBS script"""
    
    vbs_script = '''Set oWS = WScript.CreateObject("WScript.Shell")
sLinkFile = "%USERPROFILE%\\Desktop\\نظام إدارة الرواتب القطري.lnk"
Set oLink = oWS.CreateShortcut(sLinkFile)
oLink.TargetPath = WScript.Arguments(0) & "\\تشغيل_سريع.bat"
oLink.WorkingDirectory = WScript.Arguments(0)
oLink.IconLocation = WScript.Arguments(0) & "\\qatar_salary_icon.ico"
oLink.Description = "نظام إدارة الرواتب القطري - Qatar Salary Management System"
oLink.Save
'''
    
    return vbs_script

def create_autorun_inf():
    """Create autorun.inf for USB drives"""
    
    autorun_content = """[autorun]
icon=qatar_salary_icon.ico
label=نظام إدارة الرواتب القطري
action=تشغيل نظام إدارة الرواتب
open=تشغيل_سريع.bat
"""
    
    return autorun_content

def create_enhanced_portable_version():
    """Create enhanced portable version with icons and shortcuts"""
    
    print("🎨 إنشاء النسخة المحمولة المحسنة مع الأيقونات...")
    
    portable_dir = "Qatar_Salary_System_Portable"
    
    # Create icons
    print("🖼️ إنشاء الأيقونات...")
    
    try:
        # Try to create detailed icon
        icon_img = create_system_icon()
        print("   ✅ تم إنشاء الأيقونة المفصلة")
    except:
        # Fallback to simple icon
        icon_img = create_simple_icon()
        print("   ✅ تم إنشاء الأيقونة البسيطة")
    
    # Save icons
    png_path, ico_path = save_icon_formats(icon_img, 
                                          os.path.join(portable_dir, "qatar_salary_icon"))
    
    # Create shortcuts
    print("🔗 إنشاء الاختصارات...")
    
    # Linux/Mac desktop shortcut
    desktop_shortcut = create_desktop_shortcut()
    with open(os.path.join(portable_dir, "Qatar_Salary_System.desktop"), "w", encoding="utf-8") as f:
        f.write(desktop_shortcut)
    print("   ✅ تم إنشاء اختصار Linux/Mac")
    
    # Windows shortcut script
    vbs_script = create_windows_shortcut()
    with open(os.path.join(portable_dir, "create_shortcut.vbs"), "w", encoding="utf-8") as f:
        f.write(vbs_script)
    print("   ✅ تم إنشاء سكريپت اختصار Windows")
    
    # Autorun for USB
    autorun_content = create_autorun_inf()
    with open(os.path.join(portable_dir, "autorun.inf"), "w", encoding="utf-8") as f:
        f.write(autorun_content)
    print("   ✅ تم إنشاء ملف التشغيل التلقائي")
    
    # Create enhanced batch file with icon
    create_enhanced_batch_file(portable_dir)
    
    print("\n🎉 تم إنشاء النسخة المحسنة بنجاح!")
    return portable_dir

def create_enhanced_batch_file(portable_dir):
    """Create enhanced batch file with better UI"""
    
    enhanced_bat = """@echo off
chcp 65001 >nul
title نظام إدارة الرواتب القطري - Qatar Salary Management System
color 0B

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🇶🇦 نظام إدارة الرواتب القطري 🇶🇦                    ║
echo ║                Qatar Salary Management System                ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo ┌──────────────────────────────────────────────────────────────┐
echo │  💼 نظام شامل لإدارة رواتب العمال في دولة قطر                │
echo │  🚀 نسخة محمولة - تعمل مباشرة من الفلاش ميموري               │
echo │  🌐 واجهة عربية جميلة ومتجاوبة                              │
echo └──────────────────────────────────────────────────────────────┘
echo.

REM Check if Python is installed
echo 🔍 فحص متطلبات النظام...
python --version >nul 2>&1
if errorlevel 1 (
    echo.
    echo ❌ Python غير مثبت على النظام
    echo 📥 يرجى تثبيت Python من: https://python.org
    echo 💡 بعد التثبيت، شغل هذا الملف مرة أخرى
    echo.
    pause
    exit /b 1
)

echo ✅ Python موجود ومثبت
echo.

REM Check if requirements are installed
echo 📦 فحص المكتبات المطلوبة...
python -c "import flask" >nul 2>&1
if errorlevel 1 (
    echo 🔄 جاري تثبيت المكتبات المطلوبة...
    echo    هذا قد يستغرق دقيقة أو دقيقتين...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المكتبات
        echo 🌐 تأكد من الاتصال بالإنترنت وحاول مرة أخرى
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت جميع المكتبات بنجاح
) else (
    echo ✅ جميع المكتبات موجودة ومثبتة
)

echo.
echo ┌──────────────────────────────────────────────────────────────┐
echo │  🚀 جاري تشغيل النظام...                                     │
echo │                                                              │
echo │  🌐 سيتم فتح النظام في المتصفح على العنوان:                │
echo │     http://localhost:7474                                    │
echo │                                                              │
echo │  🔐 بيانات الدخول الافتراضية:                              │
echo │     اسم المستخدم: admin                                     │
echo │     كلمة المرور: admin123                                   │
echo │                                                              │
echo │  🛑 للإيقاف اضغط Ctrl+C                                     │
echo └──────────────────────────────────────────────────────────────┘
echo.

REM Start the application
python simple_app.py

echo.
echo ┌──────────────────────────────────────────────────────────────┐
echo │  ✅ تم إيقاف النظام بنجاح                                    │
echo │  🙏 شكراً لاستخدام نظام إدارة الرواتب القطري               │
echo └──────────────────────────────────────────────────────────────┘
echo.
pause
"""
    
    with open(os.path.join(portable_dir, "تشغيل_النظام_المحسن.bat"), "w", encoding="utf-8") as f:
        f.write(enhanced_bat)
    
    print("   ✅ تم إنشاء ملف التشغيل المحسن")

if __name__ == "__main__":
    try:
        # Install Pillow if not available
        from PIL import Image, ImageDraw
    except ImportError:
        print("📦 تثبيت مكتبة Pillow لإنشاء الأيقونات...")
        import subprocess
        import sys
        subprocess.check_call([sys.executable, "-m", "pip", "install", "Pillow"])
        from PIL import Image, ImageDraw
    
    create_enhanced_portable_version()

from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash

db = SQLAlchemy()

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    role = db.Column(db.String(20), default='user')  # admin, user
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def is_admin(self):
        return self.role == 'admin'

class Worker(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    nationality = db.Column(db.String(50), nullable=False)
    id_number = db.Column(db.String(50), unique=True, nullable=False)
    job_type = db.Column(db.String(50), nullable=False)
    employment_date = db.Column(db.Date, nullable=False)
    monthly_salary = db.Column(db.Float, nullable=False)
    photo_filename = db.Column(db.String(100))
    documents_path = db.Column(db.String(200))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationship with salary payments
    salary_payments = db.relationship('SalaryPayment', backref='worker', lazy=True)
    
    def __repr__(self):
        return f'<Worker {self.name}>'

class SalaryPayment(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    worker_id = db.Column(db.Integer, db.ForeignKey('worker.id'), nullable=False)
    month = db.Column(db.String(7), nullable=False)  # Format: YYYY-MM
    base_salary = db.Column(db.Float, nullable=False)
    overtime_hours = db.Column(db.Float, default=0)
    overtime_rate = db.Column(db.Float, default=0)
    bonuses = db.Column(db.Float, default=0)
    deductions = db.Column(db.Float, default=0)
    vacation_days = db.Column(db.Integer, default=0)
    total_amount = db.Column(db.Float, nullable=False)
    payment_date = db.Column(db.Date)
    payment_method = db.Column(db.String(50))  # cash, bank_transfer, check
    status = db.Column(db.String(20), default='unpaid')  # paid, unpaid, partial
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    
    def calculate_total(self):
        """Calculate total salary amount"""
        overtime_amount = self.overtime_hours * self.overtime_rate
        self.total_amount = self.base_salary + overtime_amount + self.bonuses - self.deductions
        return self.total_amount
    
    def __repr__(self):
        return f'<SalaryPayment {self.worker.name} - {self.month}>'

class SystemSettings(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(50), unique=True, nullable=False)
    value = db.Column(db.String(200), nullable=False)
    description = db.Column(db.String(200))
    
    @staticmethod
    def get_setting(key, default=None):
        setting = SystemSettings.query.filter_by(key=key).first()
        return setting.value if setting else default
    
    @staticmethod
    def set_setting(key, value, description=None):
        setting = SystemSettings.query.filter_by(key=key).first()
        if setting:
            setting.value = value
            if description:
                setting.description = description
        else:
            setting = SystemSettings(key=key, value=value, description=description)
            db.session.add(setting)
        db.session.commit()
        return setting

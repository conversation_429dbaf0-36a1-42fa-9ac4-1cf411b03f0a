@echo off
chcp 65001 >nul
title إنشاء اختصار سطح المكتب - النسخة المُصححة

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║            إنشاء اختصار على سطح المكتب - النسخة المُصححة      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

set "current_dir=%~dp0"
set "desktop=%USERPROFILE%\Desktop"
set "shortcut_name=🇶🇦 نظام الرواتب القطري (مُصحح).lnk"

echo 🔧 جاري إنشاء الاختصار...

REM Create VBS script
echo Set oWS = WScript.CreateObject("WScript.Shell") > temp_shortcut.vbs
echo sLinkFile = "%desktop%\%shortcut_name%" >> temp_shortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> temp_shortcut.vbs
echo oLink.TargetPath = "%current_dir%🚀_تشغيل_النظام_المُصحح.bat" >> temp_shortcut.vbs
echo oLink.WorkingDirectory = "%current_dir%" >> temp_shortcut.vbs
if exist "%current_dir%qatar_salary_icon.ico" (
    echo oLink.IconLocation = "%current_dir%qatar_salary_icon.ico" >> temp_shortcut.vbs
)
echo oLink.Description = "نظام إدارة الرواتب القطري - النسخة المُصححة بدون أخطاء" >> temp_shortcut.vbs
echo oLink.Save >> temp_shortcut.vbs

REM Execute VBS script
cscript //nologo temp_shortcut.vbs

REM Clean up
del temp_shortcut.vbs

if exist "%desktop%\%shortcut_name%" (
    echo ✅ تم إنشاء الاختصار بنجاح على سطح المكتب!
    echo 🎯 يمكنك الآن تشغيل النظام من سطح المكتب
    echo 🚀 النسخة المُصححة - بدون أخطاء
    echo 🎨 مع الأيقونات القطرية الجميلة
) else (
    echo ❌ فشل في إنشاء الاختصار
)

echo.
pause

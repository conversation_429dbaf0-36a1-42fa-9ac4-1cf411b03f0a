{% extends "base.html" %}

{% block title %}Users Management - Salary System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-user-cog"></i> Users Management</h1>
            <a href="#" class="btn btn-primary">
                <i class="fas fa-user-plus"></i> Add User
            </a>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-body">
        {% if users %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>Username</th>
                        <th>Email</th>
                        <th>Role</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr>
                        <td>
                            <strong>{{ user.username }}</strong>
                            {% if user.id == current_user.id %}
                                <span class="badge bg-info ms-2">You</span>
                            {% endif %}
                        </td>
                        <td>{{ user.email }}</td>
                        <td>
                            <span class="badge bg-{{ 'danger' if user.role == 'admin' else 'primary' }}">
                                {{ user.role.title() }}
                            </span>
                        </td>
                        <td>
                            <span class="badge bg-{{ 'success' if user.is_active else 'secondary' }}">
                                {{ 'Active' if user.is_active else 'Inactive' }}
                            </span>
                        </td>
                        <td>{{ user.created_at.strftime('%Y-%m-%d') }}</td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="#" class="btn btn-outline-primary" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if user.id != current_user.id %}
                                <button type="button" class="btn btn-outline-danger" title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">No users found</h4>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

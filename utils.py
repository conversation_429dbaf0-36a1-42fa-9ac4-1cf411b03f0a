import os
from io import BytesIO
from datetime import datetime
from flask import current_app
from models import db, User, SystemSettings

# Try to import reportlab, but make it optional
try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

def allowed_file(filename):
    """Check if file extension is allowed"""
    if not filename:
        return False
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']

def create_default_settings():
    """Create default system settings"""
    default_settings = [
        ('company_name', 'Domestic Workers Management System', 'Company or sponsor name'),
        ('default_currency', 'SAR', 'Default currency for salary calculations'),
        ('default_language', 'en', 'Default system language'),
        ('overtime_rate', '15.0', 'Default overtime rate per hour'),
        ('vacation_days_per_year', '30', 'Default vacation days per year')
    ]
    
    for key, value, description in default_settings:
        if not SystemSettings.query.filter_by(key=key).first():
            setting = SystemSettings(key=key, value=value, description=description)
            db.session.add(setting)
    
    try:
        db.session.commit()
    except:
        db.session.rollback()

def init_admin_user():
    """Create default admin user if none exists"""
    if not User.query.filter_by(role='admin').first():
        admin = User(
            username='admin',
            email='<EMAIL>',
            role='admin',
            is_active=True
        )
        admin.set_password('admin123')  # Change this in production!
        db.session.add(admin)
        try:
            db.session.commit()
        except:
            db.session.rollback()

def generate_salary_receipt(salary_payment, language='en'):
    """Generate PDF salary receipt"""
    if not REPORTLAB_AVAILABLE:
        # Return a simple text receipt if reportlab is not available
        buffer = BytesIO()
        worker = salary_payment.worker
        currency = SystemSettings.get_setting('default_currency', 'SAR')

        if language == 'ar':
            content = f"""
إيصال راتب
==========

اسم العامل: {worker.name}
الجنسية: {worker.nationality}
رقم الهوية: {worker.id_number}
نوع العمل: {worker.job_type}
الشهر: {salary_payment.month}
الراتب الأساسي: {salary_payment.base_salary} {currency}
ساعات إضافية: {salary_payment.overtime_hours} ساعة
أجر الساعات الإضافية: {salary_payment.overtime_hours * salary_payment.overtime_rate} {currency}
المكافآت: {salary_payment.bonuses} {currency}
الخصومات: {salary_payment.deductions} {currency}
إجمالي الراتب: {salary_payment.total_amount} {currency}
تاريخ الدفع: {salary_payment.payment_date.strftime('%Y-%m-%d') if salary_payment.payment_date else 'غير محدد'}
طريقة الدفع: {salary_payment.payment_method or 'غير محدد'}
الحالة: {salary_payment.status}

التوقيع: ________________    التاريخ: ________________
الختم:
"""
        else:
            content = f"""
SALARY RECEIPT
==============

Worker Name: {worker.name}
Nationality: {worker.nationality}
ID Number: {worker.id_number}
Job Type: {worker.job_type}
Month: {salary_payment.month}
Base Salary: {salary_payment.base_salary} {currency}
Overtime Hours: {salary_payment.overtime_hours} hours
Overtime Amount: {salary_payment.overtime_hours * salary_payment.overtime_rate} {currency}
Bonuses: {salary_payment.bonuses} {currency}
Deductions: {salary_payment.deductions} {currency}
Total Amount: {salary_payment.total_amount} {currency}
Payment Date: {salary_payment.payment_date.strftime('%Y-%m-%d') if salary_payment.payment_date else 'Not Set'}
Payment Method: {salary_payment.payment_method or 'Not Set'}
Status: {salary_payment.status}

Signature: ________________    Date: ________________
Stamp:
"""

        buffer.write(content.encode('utf-8'))
        buffer.seek(0)
        return buffer

    # Full PDF generation with reportlab
    buffer = BytesIO()

    # Create PDF document
    doc = SimpleDocTemplate(buffer, pagesize=A4)
    styles = getSampleStyleSheet()
    story = []

    # Title
    title_style = ParagraphStyle(
        'CustomTitle',
        parent=styles['Heading1'],
        fontSize=18,
        spaceAfter=30,
        alignment=1  # Center alignment
    )

    if language == 'ar':
        title = "إيصال راتب"
        company_name = SystemSettings.get_setting('company_name', 'نظام إدارة العمالة المنزلية')
    else:
        title = "Salary Receipt"
        company_name = SystemSettings.get_setting('company_name', 'Domestic Workers Management System')

    story.append(Paragraph(company_name, title_style))
    story.append(Paragraph(title, title_style))
    story.append(Spacer(1, 20))

    # Worker and payment details
    worker = salary_payment.worker
    currency = SystemSettings.get_setting('default_currency', 'SAR')

    if language == 'ar':
        data = [
            ['اسم العامل:', worker.name],
            ['الجنسية:', worker.nationality],
            ['رقم الهوية:', worker.id_number],
            ['نوع العمل:', worker.job_type],
            ['الشهر:', salary_payment.month],
            ['الراتب الأساسي:', f"{salary_payment.base_salary} {currency}"],
            ['ساعات إضافية:', f"{salary_payment.overtime_hours} ساعة"],
            ['أجر الساعات الإضافية:', f"{salary_payment.overtime_hours * salary_payment.overtime_rate} {currency}"],
            ['المكافآت:', f"{salary_payment.bonuses} {currency}"],
            ['الخصومات:', f"{salary_payment.deductions} {currency}"],
            ['إجمالي الراتب:', f"{salary_payment.total_amount} {currency}"],
            ['تاريخ الدفع:', salary_payment.payment_date.strftime('%Y-%m-%d') if salary_payment.payment_date else 'غير محدد'],
            ['طريقة الدفع:', salary_payment.payment_method or 'غير محدد'],
            ['الحالة:', salary_payment.status]
        ]
    else:
        data = [
            ['Worker Name:', worker.name],
            ['Nationality:', worker.nationality],
            ['ID Number:', worker.id_number],
            ['Job Type:', worker.job_type],
            ['Month:', salary_payment.month],
            ['Base Salary:', f"{salary_payment.base_salary} {currency}"],
            ['Overtime Hours:', f"{salary_payment.overtime_hours} hours"],
            ['Overtime Amount:', f"{salary_payment.overtime_hours * salary_payment.overtime_rate} {currency}"],
            ['Bonuses:', f"{salary_payment.bonuses} {currency}"],
            ['Deductions:', f"{salary_payment.deductions} {currency}"],
            ['Total Amount:', f"{salary_payment.total_amount} {currency}"],
            ['Payment Date:', salary_payment.payment_date.strftime('%Y-%m-%d') if salary_payment.payment_date else 'Not Set'],
            ['Payment Method:', salary_payment.payment_method or 'Not Set'],
            ['Status:', salary_payment.status]
        ]

    # Create table
    table = Table(data, colWidths=[2*inch, 3*inch])
    table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 12),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('FONTSIZE', (0, 1), (-1, -1), 10),
    ]))

    story.append(table)
    story.append(Spacer(1, 30))

    # Signature section
    if language == 'ar':
        signature_text = "التوقيع: ________________    التاريخ: ________________"
        stamp_text = "الختم:"
    else:
        signature_text = "Signature: ________________    Date: ________________"
        stamp_text = "Stamp:"

    story.append(Paragraph(signature_text, styles['Normal']))
    story.append(Spacer(1, 20))
    story.append(Paragraph(stamp_text, styles['Normal']))

    # Build PDF
    doc.build(story)
    buffer.seek(0)
    return buffer

def format_currency(amount, currency=None):
    """Format currency amount"""
    if currency is None:
        currency = SystemSettings.get_setting('default_currency', 'SAR')
    return f"{amount:,.2f} {currency}"

def get_month_name(month_str, language='en'):
    """Get month name in specified language"""
    try:
        year, month = month_str.split('-')
        month_num = int(month)
        
        if language == 'ar':
            months_ar = [
                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ]
            return f"{months_ar[month_num-1]} {year}"
        else:
            months_en = [
                'January', 'February', 'March', 'April', 'May', 'June',
                'July', 'August', 'September', 'October', 'November', 'December'
            ]
            return f"{months_en[month_num-1]} {year}"
    except:
        return month_str

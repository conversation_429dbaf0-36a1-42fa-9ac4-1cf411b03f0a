{% extends "simple_base.html" %}

{% block title %}تقرير العمال - نظام الرواتب{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-users"></i> تقرير العمال</h1>
            <div>
                <a href="{{ url_for('export_report', report_type='workers') }}" class="btn btn-success me-2">
                    <i class="fas fa-file-csv"></i> تصدير CSV
                </a>
                <a href="{{ url_for('reports') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> العودة للتقارير
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>{{ total_workers }}</h3>
                        <p class="mb-0">العمال النشطون</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-secondary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>{{ inactive_workers }}</h3>
                        <p class="mb-0">العمال غير النشطين</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-slash fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>{{ nationality_stats|length }}</h3>
                        <p class="mb-0">جنسيات مختلفة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-flag fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>{{ job_type_stats|length }}</h3>
                        <p class="mb-0">أنواع عمل مختلفة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-briefcase fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Nationality Distribution -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-flag"></i> توزيع العمال حسب الجنسية</h5>
            </div>
            <div class="card-body">
                {% if nationality_stats %}
                    {% for nationality, count in nationality_stats %}
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <i class="fas fa-flag text-primary"></i>
                            <strong>{{ nationality }}</strong>
                        </div>
                        <div>
                            <span class="badge bg-primary">{{ count }} عامل</span>
                            <small class="text-muted ms-2">
                                {{ "%.1f"|format((count / total_workers * 100) if total_workers > 0 else 0) }}%
                            </small>
                        </div>
                    </div>
                    <div class="progress mb-3" style="height: 8px;">
                        <div class="progress-bar" role="progressbar" 
                             style="width: {{ (count / total_workers * 100) if total_workers > 0 else 0 }}%">
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted text-center">لا توجد بيانات</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Job Type Distribution -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-briefcase"></i> توزيع العمال حسب نوع العمل</h5>
            </div>
            <div class="card-body">
                {% if job_type_stats %}
                    {% for job_type, count in job_type_stats %}
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <i class="fas fa-briefcase text-success"></i>
                            <strong>
                                {% if job_type == 'housemaid' %}خادمة منزل
                                {% elif job_type == 'driver' %}سائق
                                {% elif job_type == 'cook' %}طباخ
                                {% elif job_type == 'nanny' %}مربية أطفال
                                {% elif job_type == 'gardener' %}بستاني
                                {% elif job_type == 'cleaner' %}عامل نظافة
                                {% elif job_type == 'security_guard' %}حارس أمن
                                {% elif job_type == 'maintenance_worker' %}عامل صيانة
                                {% else %}{{ job_type }}
                                {% endif %}
                            </strong>
                        </div>
                        <div>
                            <span class="badge bg-success">{{ count }} عامل</span>
                            <small class="text-muted ms-2">
                                {{ "%.1f"|format((count / total_workers * 100) if total_workers > 0 else 0) }}%
                            </small>
                        </div>
                    </div>
                    <div class="progress mb-3" style="height: 8px;">
                        <div class="progress-bar bg-success" role="progressbar" 
                             style="width: {{ (count / total_workers * 100) if total_workers > 0 else 0 }}%">
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted text-center">لا توجد بيانات</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Recent Workers -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-clock"></i> العمال المضافون حديثاً</h5>
            </div>
            <div class="card-body">
                {% if recent_workers %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>الجنسية</th>
                                <th>نوع العمل</th>
                                <th>الراتب الشهري</th>
                                <th>تاريخ التوظيف</th>
                                <th>تاريخ الإضافة</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for worker in recent_workers %}
                            <tr>
                                <td><strong>{{ worker.name }}</strong></td>
                                <td>
                                    <i class="fas fa-flag"></i> {{ worker.nationality }}
                                </td>
                                <td>
                                    <span class="badge bg-info">
                                        {% if worker.job_type == 'housemaid' %}خادمة منزل
                                        {% elif worker.job_type == 'driver' %}سائق
                                        {% elif worker.job_type == 'cook' %}طباخ
                                        {% elif worker.job_type == 'nanny' %}مربية أطفال
                                        {% elif worker.job_type == 'gardener' %}بستاني
                                        {% elif worker.job_type == 'cleaner' %}عامل نظافة
                                        {% elif worker.job_type == 'security_guard' %}حارس أمن
                                        {% elif worker.job_type == 'maintenance_worker' %}عامل صيانة
                                        {% else %}{{ worker.job_type }}
                                        {% endif %}
                                    </span>
                                </td>
                                <td>{{ "%.2f"|format(worker.monthly_salary) }} ر.ق</td>
                                <td>{{ worker.employment_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ worker.created_at.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <span class="badge bg-{{ 'success' if worker.is_active else 'secondary' }}">
                                        {{ 'نشط' if worker.is_active else 'غير نشط' }}
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted text-center">لا يوجد عمال</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% extends "base.html" %}

{% block title %}{{ title }} - Salary System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-{{ 'plus' if 'Add' in title else 'edit' }}"></i> {{ title }}</h1>
            <a href="{{ url_for('salary_payments') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Payments
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card">
            <div class="card-body">
                <form method="POST" id="salaryForm">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <!-- Worker and Basic Info -->
                        <div class="col-md-6">
                            <h5 class="mb-3"><i class="fas fa-user"></i> Worker Information</h5>
                            
                            <div class="mb-3">
                                {{ form.worker_id.label(class="form-label") }}
                                {{ form.worker_id(class="form-select" + (" is-invalid" if form.worker_id.errors else ""), onchange="loadWorkerSalary()") }}
                                {% if form.worker_id.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.worker_id.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                {{ form.month.label(class="form-label") }}
                                {{ form.month(class="form-control" + (" is-invalid" if form.month.errors else ""), placeholder="YYYY-MM") }}
                                {% if form.month.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.month.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">Format: YYYY-MM (e.g., 2024-01)</div>
                            </div>
                            
                            <div class="mb-3">
                                {{ form.base_salary.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.base_salary(class="form-control" + (" is-invalid" if form.base_salary.errors else ""), onchange="calculateTotal()") }}
                                    <span class="input-group-text">SAR</span>
                                </div>
                                {% if form.base_salary.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.base_salary.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- Overtime and Adjustments -->
                        <div class="col-md-6">
                            <h5 class="mb-3"><i class="fas fa-calculator"></i> Calculations</h5>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        {{ form.overtime_hours.label(class="form-label") }}
                                        {{ form.overtime_hours(class="form-control" + (" is-invalid" if form.overtime_hours.errors else ""), onchange="calculateTotal()") }}
                                        {% if form.overtime_hours.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.overtime_hours.errors %}{{ error }}{% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        {{ form.overtime_rate.label(class="form-label") }}
                                        <div class="input-group">
                                            {{ form.overtime_rate(class="form-control" + (" is-invalid" if form.overtime_rate.errors else ""), onchange="calculateTotal()") }}
                                            <span class="input-group-text">SAR/hr</span>
                                        </div>
                                        {% if form.overtime_rate.errors %}
                                            <div class="invalid-feedback">
                                                {% for error in form.overtime_rate.errors %}{{ error }}{% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                {{ form.bonuses.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.bonuses(class="form-control" + (" is-invalid" if form.bonuses.errors else ""), onchange="calculateTotal()") }}
                                    <span class="input-group-text">SAR</span>
                                </div>
                                {% if form.bonuses.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.bonuses.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                {{ form.deductions.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.deductions(class="form-control" + (" is-invalid" if form.deductions.errors else ""), onchange="calculateTotal()") }}
                                    <span class="input-group-text">SAR</span>
                                </div>
                                {% if form.deductions.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.deductions.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                {{ form.vacation_days.label(class="form-label") }}
                                {{ form.vacation_days(class="form-control" + (" is-invalid" if form.vacation_days.errors else "")) }}
                                {% if form.vacation_days.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.vacation_days.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <!-- Total Calculation Display -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5><i class="fas fa-calculator"></i> Salary Calculation</h5>
                                    <div class="row">
                                        <div class="col-md-3">
                                            <strong>Base Salary:</strong><br>
                                            <span id="displayBaseSalary">0.00 SAR</span>
                                        </div>
                                        <div class="col-md-3">
                                            <strong>Overtime:</strong><br>
                                            <span id="displayOvertime">0.00 SAR</span>
                                        </div>
                                        <div class="col-md-3">
                                            <strong>Bonuses:</strong><br>
                                            <span id="displayBonuses">0.00 SAR</span>
                                        </div>
                                        <div class="col-md-3">
                                            <strong>Deductions:</strong><br>
                                            <span id="displayDeductions">0.00 SAR</span>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="text-center">
                                        <h4><strong>Total Amount: <span id="displayTotal" class="text-primary">0.00 SAR</span></strong></h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <!-- Payment Details -->
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="mb-3"><i class="fas fa-credit-card"></i> Payment Details</h5>
                            
                            <div class="mb-3">
                                {{ form.payment_date.label(class="form-label") }}
                                {{ form.payment_date(class="form-control" + (" is-invalid" if form.payment_date.errors else "")) }}
                                {% if form.payment_date.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.payment_date.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                {{ form.payment_method.label(class="form-label") }}
                                {{ form.payment_method(class="form-select" + (" is-invalid" if form.payment_method.errors else "")) }}
                                {% if form.payment_method.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.payment_method.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                {{ form.status.label(class="form-label") }}
                                {{ form.status(class="form-select" + (" is-invalid" if form.status.errors else "")) }}
                                {% if form.status.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.status.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h5 class="mb-3"><i class="fas fa-sticky-note"></i> Notes</h5>
                            
                            <div class="mb-3">
                                {{ form.notes.label(class="form-label") }}
                                {{ form.notes(class="form-control" + (" is-invalid" if form.notes.errors else ""), rows="6") }}
                                {% if form.notes.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.notes.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <!-- Form Actions -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="{{ url_for('salary_payments') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> {{ 'Update' if 'Edit' in title else 'Save' }} Payment
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Worker salary data (populated by server)
const workerSalaries = {{ worker_salaries|tojson if worker_salaries else '{}' }};

function loadWorkerSalary() {
    const workerId = document.querySelector('select[name="worker_id"]').value;
    if (workerId && workerSalaries[workerId]) {
        document.querySelector('input[name="base_salary"]').value = workerSalaries[workerId];
        calculateTotal();
    }
}

function calculateTotal() {
    const baseSalary = parseFloat(document.querySelector('input[name="base_salary"]').value) || 0;
    const overtimeHours = parseFloat(document.querySelector('input[name="overtime_hours"]').value) || 0;
    const overtimeRate = parseFloat(document.querySelector('input[name="overtime_rate"]').value) || 0;
    const bonuses = parseFloat(document.querySelector('input[name="bonuses"]').value) || 0;
    const deductions = parseFloat(document.querySelector('input[name="deductions"]').value) || 0;
    
    const overtimeAmount = overtimeHours * overtimeRate;
    const totalAmount = baseSalary + overtimeAmount + bonuses - deductions;
    
    // Update display
    document.getElementById('displayBaseSalary').textContent = baseSalary.toFixed(2) + ' SAR';
    document.getElementById('displayOvertime').textContent = overtimeAmount.toFixed(2) + ' SAR';
    document.getElementById('displayBonuses').textContent = bonuses.toFixed(2) + ' SAR';
    document.getElementById('displayDeductions').textContent = deductions.toFixed(2) + ' SAR';
    document.getElementById('displayTotal').textContent = totalAmount.toFixed(2) + ' SAR';
}

// Set current month as default
document.addEventListener('DOMContentLoaded', function() {
    const monthField = document.querySelector('input[name="month"]');
    if (!monthField.value) {
        const now = new Date();
        const currentMonth = now.getFullYear() + '-' + String(now.getMonth() + 1).padStart(2, '0');
        monthField.value = currentMonth;
    }
    
    // Initial calculation
    calculateTotal();
});
</script>
{% endblock %}

@echo off
echo ========================================
echo    نظام إدارة الرواتب القطري
echo    Qatar Salary Management System
echo ========================================
echo.
echo جاري تثبيت المتطلبات...
echo Installing requirements...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo ❌ Python is not installed
    echo.
    echo يرجى تثبيت Python من: https://python.org
    echo Please install Python from: https://python.org
    pause
    exit /b 1
)

REM Install requirements
pip install -r requirements.txt

if errorlevel 1 (
    echo ❌ فشل في تثبيت المتطلبات
    echo ❌ Failed to install requirements
    pause
    exit /b 1
)

echo.
echo ✅ تم تثبيت المتطلبات بنجاح
echo ✅ Requirements installed successfully
echo.
echo يمكنك الآن تشغيل النظام باستخدام: run.bat
echo You can now run the system using: run.bat
echo.
pause

{% extends "base.html" %}

{% block title %}{{ worker.name }} - Worker Details{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-user"></i> {{ worker.name }}</h1>
            <div>
                <a href="{{ url_for('edit_worker', id=worker.id) }}" class="btn btn-primary me-2">
                    <i class="fas fa-edit"></i> Edit Worker
                </a>
                <a href="{{ url_for('workers') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Workers
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> Worker Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="40%">Name:</th>
                                <td>{{ worker.name }}</td>
                            </tr>
                            <tr>
                                <th>Nationality:</th>
                                <td>{{ worker.nationality }}</td>
                            </tr>
                            <tr>
                                <th>ID Number:</th>
                                <td>{{ worker.id_number }}</td>
                            </tr>
                            <tr>
                                <th>Job Type:</th>
                                <td>
                                    <span class="badge bg-info">{{ worker.job_type.replace('_', ' ').title() }}</span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th width="40%">Employment Date:</th>
                                <td>{{ worker.employment_date.strftime('%Y-%m-%d') }}</td>
                            </tr>
                            <tr>
                                <th>Monthly Salary:</th>
                                <td><strong>{{ "%.2f"|format(worker.monthly_salary) }} SAR</strong></td>
                            </tr>
                            <tr>
                                <th>Status:</th>
                                <td>
                                    <span class="badge bg-{{ 'success' if worker.is_active else 'secondary' }}">
                                        {{ 'Active' if worker.is_active else 'Inactive' }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <th>Added:</th>
                                <td>{{ worker.created_at.strftime('%Y-%m-%d') }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-money-bill-wave"></i> Recent Salary Payments</h5>
            </div>
            <div class="card-body">
                {% if recent_payments %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Month</th>
                                <th>Base Salary</th>
                                <th>Total Amount</th>
                                <th>Status</th>
                                <th>Payment Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payment in recent_payments %}
                            <tr>
                                <td>{{ payment.month }}</td>
                                <td>{{ "%.2f"|format(payment.base_salary) }} SAR</td>
                                <td><strong>{{ "%.2f"|format(payment.total_amount) }} SAR</strong></td>
                                <td>
                                    <span class="badge bg-{{ 'success' if payment.status == 'paid' else 'warning' if payment.status == 'partial' else 'danger' }}">
                                        {{ payment.status.title() }}
                                    </span>
                                </td>
                                <td>
                                    {% if payment.payment_date %}
                                        {{ payment.payment_date.strftime('%Y-%m-%d') }}
                                    {% else %}
                                        <span class="text-muted">Not set</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{{ url_for('view_salary_payment', id=payment.id) }}" 
                                       class="btn btn-sm btn-outline-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="text-end">
                    <a href="{{ url_for('salary_payments', worker_id=worker.id) }}" class="btn btn-sm btn-primary">
                        View All Payments
                    </a>
                </div>
                {% else %}
                <div class="text-center py-3">
                    <i class="fas fa-money-bill-wave fa-2x text-muted mb-2"></i>
                    <p class="text-muted">No salary payments recorded yet.</p>
                    <a href="{{ url_for('add_salary_payment') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Payment
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-camera"></i> Photo</h5>
            </div>
            <div class="card-body text-center">
                {% if worker.photo_filename %}
                    <img src="{{ url_for('uploaded_file', filename='photos/' + worker.photo_filename) }}" 
                         class="img-fluid rounded" style="max-height: 200px;" alt="Worker Photo">
                {% else %}
                    <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                         style="height: 200px;">
                        <i class="fas fa-user fa-4x text-muted"></i>
                    </div>
                    <p class="text-muted mt-2">No photo uploaded</p>
                {% endif %}
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-file-alt"></i> Documents</h5>
            </div>
            <div class="card-body">
                {% if worker.documents_path %}
                    <div class="text-center">
                        <i class="fas fa-file-pdf fa-3x text-danger mb-2"></i>
                        <p>Documents available</p>
                        <a href="{{ url_for('uploaded_file', filename='documents/' + worker.documents_path) }}" 
                           class="btn btn-sm btn-outline-primary" target="_blank">
                            <i class="fas fa-download"></i> Download
                        </a>
                    </div>
                {% else %}
                    <div class="text-center">
                        <i class="fas fa-file fa-3x text-muted mb-2"></i>
                        <p class="text-muted">No documents uploaded</p>
                    </div>
                {% endif %}
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-chart-bar"></i> Quick Stats</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 mb-2">
                        <h4 class="text-primary">{{ worker.salary_payments|length }}</h4>
                        <small class="text-muted">Total Payments</small>
                    </div>
                    <div class="col-6 mb-2">
                        <h4 class="text-success">
                            {{ worker.salary_payments|selectattr("status", "equalto", "paid")|list|length }}
                        </h4>
                        <small class="text-muted">Paid</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-warning">
                            {{ worker.salary_payments|selectattr("status", "equalto", "unpaid")|list|length }}
                        </h4>
                        <small class="text-muted">Unpaid</small>
                    </div>
                    <div class="col-6">
                        <h4 class="text-info">
                            {% set total = worker.salary_payments|selectattr("status", "equalto", "paid")|map(attribute="total_amount")|sum %}
                            {{ "%.0f"|format(total) }}
                        </h4>
                        <small class="text-muted">Total Paid (SAR)</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-tools"></i> Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('edit_worker', id=worker.id) }}" class="btn btn-primary">
                        <i class="fas fa-edit"></i> Edit Worker
                    </a>
                    <a href="{{ url_for('add_salary_payment') }}?worker_id={{ worker.id }}" class="btn btn-success">
                        <i class="fas fa-money-bill-wave"></i> Add Payment
                    </a>
                    <a href="{{ url_for('salary_payments', worker_id=worker.id) }}" class="btn btn-info">
                        <i class="fas fa-history"></i> Payment History
                    </a>
                    {% if current_user.is_admin() and not worker.salary_payments %}
                    <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                        <i class="fas fa-trash"></i> Delete Worker
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
{% if current_user.is_admin() and not worker.salary_payments %}
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete worker <strong>{{ worker.name }}</strong>?</p>
                <p class="text-danger"><small>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="POST" action="{{ url_for('delete_worker', id=worker.id) }}" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
{% if current_user.is_admin() and not worker.salary_payments %}
<script>
function confirmDelete() {
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endif %}
{% endblock %}

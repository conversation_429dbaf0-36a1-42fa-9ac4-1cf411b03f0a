#!/bin/bash
echo "========================================"
echo "   نظام إدارة الرواتب القطري"
echo "   Qatar Salary Management System"
echo "========================================"
echo ""
echo "جاري تثبيت المتطلبات..."
echo "Installing requirements..."
echo ""

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ Python غير مثبت على النظام"
        echo "❌ Python is not installed"
        echo ""
        echo "يرجى تثبيت Python من: https://python.org"
        echo "Please install Python from: https://python.org"
        exit 1
    fi
    PYTHON_CMD="python"
else
    PYTHON_CMD="python3"
fi

# Install requirements
$PYTHON_CMD -m pip install -r requirements.txt

if [ $? -ne 0 ]; then
    echo "❌ فشل في تثبيت المتطلبات"
    echo "❌ Failed to install requirements"
    exit 1
fi

echo ""
echo "✅ تم تثبيت المتطلبات بنجاح"
echo "✅ Requirements installed successfully"
echo ""
echo "يمكنك الآن تشغيل النظام باستخدام: ./run.sh"
echo "You can now run the system using: ./run.sh"
echo ""

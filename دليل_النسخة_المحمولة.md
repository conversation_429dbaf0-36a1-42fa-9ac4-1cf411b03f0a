# دليل النسخة المحمولة لنظام إدارة الرواتب القطري 💾

## نظرة عامة
تم إنشاء نسخة محمولة من نظام إدارة الرواتب القطري تعمل مباشرة من الفلاش ميموري أو أي وسيط تخزين محمول بدون الحاجة لتثبيت معقد.

## 🎯 أنواع النسخ المحمولة

### 1. **النسخة المحمولة العادية** 📁
- **المجلد:** `Qatar_Salary_System_Portable`
- **الملف المضغوط:** `Qatar_Salary_System_Portable.zip`
- **المتطلبات:** Python مثبت على النظام
- **الحجم:** ~5 MB
- **المميزات:** سريعة وخفيفة

### 2. **النسخة المستقلة** 🚀
- **المجلد:** `Qatar_Salary_System_Standalone`
- **الملف المضغوط:** `Qatar_Salary_System_Standalone.zip`
- **المتطلبات:** لا تحتاج Python
- **الحجم:** ~50-100 MB
- **المميزات:** تعمل على أي جهاز

## 📦 محتويات النسخة المحمولة

### الملفات الأساسية:
```
Qatar_Salary_System_Portable/
├── simple_app.py              # التطبيق الرئيسي
├── requirements.txt           # المكتبات المطلوبة
├── templates/                 # قوالب الواجهة
│   ├── simple_base.html
│   ├── simple_workers.html
│   ├── simple_worker_form.html
│   └── ...
├── static/                    # الملفات الثابتة
│   ├── css/
│   ├── js/
│   └── images/
├── install.bat               # تثبيت Windows
├── run.bat                   # تشغيل Windows
├── install.sh                # تثبيت Linux/Mac
├── run.sh                    # تشغيل Linux/Mac
└── README_PORTABLE.md        # دليل الاستخدام
```

## 🚀 طريقة التشغيل

### على Windows:
1. **فك الضغط** عن الملف المضغوط
2. **نسخ المجلد** إلى الفلاش ميموري
3. **تشغيل** `install.bat` (مرة واحدة فقط)
4. **تشغيل** `run.bat` لبدء النظام
5. **فتح المتصفح** على: http://localhost:7474

### على Linux/Mac:
1. **فك الضغط** عن الملف المضغوط
2. **نسخ المجلد** إلى الفلاش ميموري
3. **تشغيل** `./install.sh` (مرة واحدة فقط)
4. **تشغيل** `./run.sh` لبدء النظام
5. **فتح المتصفح** على: http://localhost:7474

## 🔐 بيانات الدخول الافتراضية

- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

## 📋 المتطلبات

### للنسخة المحمولة العادية:
- **Python 3.7+** مثبت على النظام
- **اتصال إنترنت** لتثبيت المكتبات (أول مرة فقط)
- **100 MB** مساحة فارغة
- **512 MB** ذاكرة عشوائية

### للنسخة المستقلة:
- **لا تحتاج Python**
- **لا تحتاج اتصال إنترنت**
- **100 MB** مساحة فارغة
- **512 MB** ذاكرة عشوائية

## ✨ مميزات النسخة المحمولة

### 🎯 **سهولة الاستخدام:**
- **تشغيل مباشر** من الفلاش ميموري
- **لا تحتاج تثبيت معقد**
- **ملفات تشغيل جاهزة** لجميع الأنظمة
- **واجهة عربية/إنجليزية**

### 🔧 **المرونة:**
- **تعمل على أي جهاز** كمبيوتر
- **لا تترك أثر** على النظام
- **قاعدة بيانات محمولة** تنتقل معك
- **نسخ احتياطية سهلة**

### 🚀 **الأداء:**
- **تشغيل سريع** (أقل من 10 ثوان)
- **استهلاك ذاكرة قليل**
- **واجهة سريعة الاستجابة**
- **قاعدة بيانات محسنة**

## 🛠️ ملفات التشغيل

### Windows Batch Files:

#### install.bat:
```batch
@echo off
echo جاري تثبيت المتطلبات...
pip install -r requirements.txt
echo ✅ تم التثبيت بنجاح
pause
```

#### run.bat:
```batch
@echo off
echo جاري تشغيل النظام...
echo سيتم فتح النظام على: http://localhost:7474
python simple_app.py
pause
```

### Unix Shell Scripts:

#### install.sh:
```bash
#!/bin/bash
echo "جاري تثبيت المتطلبات..."
python3 -m pip install -r requirements.txt
echo "✅ تم التثبيت بنجاح"
```

#### run.sh:
```bash
#!/bin/bash
echo "جاري تشغيل النظام..."
echo "سيتم فتح النظام على: http://localhost:7474"
python3 simple_app.py
```

## 📊 الميزات المتاحة

### ✅ **إدارة العمال:**
- إضافة وتعديل وحذف العمال
- تصنيف حسب الجنس والجنسية
- معلومات شاملة لكل عامل
- تتبع حالة العمال (نشط/غير نشط)

### ✅ **إدارة الرواتب:**
- حساب الرواتب تلقائياً
- إضافة مكافآت وخصومات
- تتبع حالة الدفع
- تاريخ شامل للرواتب

### ✅ **التقارير:**
- تقارير العمال
- تقارير الرواتب
- تقارير شهرية
- تصدير البيانات

### ✅ **الواجهة:**
- شريط متحرك للرواتب
- واجهة عربية جميلة
- تصميم متجاوب
- أيقونات واضحة

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### 1. **Python غير موجود:**
```
❌ المشكلة: 'python' is not recognized
✅ الحل: تثبيت Python من python.org
```

#### 2. **فشل تثبيت المكتبات:**
```
❌ المشكلة: pip install failed
✅ الحل: تحديث pip: python -m pip install --upgrade pip
```

#### 3. **المنفذ مشغول:**
```
❌ المشكلة: Port 7474 is already in use
✅ الحل: إغلاق التطبيقات الأخرى أو تغيير المنفذ
```

#### 4. **مشاكل الصلاحيات:**
```
❌ المشكلة: Permission denied
✅ الحل: تشغيل كمدير (Run as Administrator)
```

## 📱 التوافق

### أنظمة التشغيل المدعومة:
- ✅ **Windows 7/8/10/11**
- ✅ **Linux (Ubuntu, CentOS, etc.)**
- ✅ **macOS 10.12+**

### المتصفحات المدعومة:
- ✅ **Chrome/Chromium**
- ✅ **Firefox**
- ✅ **Safari**
- ✅ **Edge**

## 🔄 التحديثات

### لتحديث النسخة المحمولة:
1. **نسخ احتياطي** لقاعدة البيانات
2. **تحميل النسخة الجديدة**
3. **نسخ قاعدة البيانات** للنسخة الجديدة
4. **تشغيل النسخة الجديدة**

### ملفات قاعدة البيانات:
- `qatar_salary_system.db` - قاعدة البيانات الرئيسية
- `instance/qatar_salary_system.db` - نسخة احتياطية

## 📦 إنشاء النسخة المحمولة

### لإنشاء نسخة محمولة جديدة:
```bash
# تشغيل سكريپت الإنشاء
python create_portable_version.py

# أو لإنشاء نسخة مستقلة
python create_standalone_version.py
```

## 🎯 حالات الاستخدام

### 1. **للشركات الصغيرة:**
- نسخ النظام على فلاش ميموري
- استخدام على أجهزة مختلفة
- عدم الحاجة لخادم مركزي

### 2. **للاستشاريين:**
- حمل النظام للعملاء
- عروض تقديمية
- تدريب الموظفين

### 3. **للاستخدام المؤقت:**
- أجهزة مؤقتة
- أجهزة مشتركة
- بيئات اختبار

## 🔒 الأمان

### اعتبارات الأمان:
- **كلمة مرور قوية** للمدير
- **نسخ احتياطية منتظمة**
- **تشفير الفلاش ميموري** (اختياري)
- **تحديثات دورية**

## 📞 الدعم والمساعدة

### للحصول على المساعدة:
- **قراءة الدليل** بعناية
- **مراجعة الأخطاء الشائعة**
- **التواصل مع فريق التطوير**
- **مراجعة ملفات السجل**

---

**النسخة المحمولة توفر مرونة كاملة لاستخدام نظام إدارة الرواتب في أي مكان وعلى أي جهاز! 🇶🇦**

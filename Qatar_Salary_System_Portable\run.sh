#!/bin/bash
echo "========================================"
echo "   نظام إدارة الرواتب القطري"
echo "   Qatar Salary Management System"
echo "========================================"
echo ""
echo "جاري تشغيل النظام..."
echo "Starting the system..."
echo ""
echo "سيتم فتح النظام في المتصفح على العنوان:"
echo "The system will open in browser at:"
echo "http://localhost:7474"
echo ""
echo "للإيقاف اضغط Ctrl+C"
echo "To stop press Ctrl+C"
echo ""

# Check if Python is installed
if command -v python3 &> /dev/null; then
    python3 simple_app.py
elif command -v python &> /dev/null; then
    python simple_app.py
else
    echo "❌ Python غير مثبت على النظام"
    echo "❌ Python is not installed"
    exit 1
fi

echo ""
echo "تم إيقاف النظام"
echo "System stopped"

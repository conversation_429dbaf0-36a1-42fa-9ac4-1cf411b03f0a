{% extends "simple_base.html" %}

{% block title %}{{ title }} - نظام الرواتب{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-user-{{ 'plus' if 'إضافة' in title else 'edit' }}"></i> {{ title }}</h1>
            <a href="{{ url_for('users') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> العودة للمستخدمين
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <h5 class="mb-3"><i class="fas fa-user"></i> معلومات المستخدم</h5>
                            
                            <div class="mb-3">
                                <label class="form-label">اسم المستخدم</label>
                                <input type="text" name="username" class="form-control" 
                                       value="{{ user.username if user else '' }}" required>
                                <div class="form-text">
                                    <small>يجب أن يكون أكثر من 3 أحرف وفريد</small>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" name="email" class="form-control" 
                                       value="{{ user.email if user else '' }}" required>
                                <div class="form-text">
                                    <small>يجب أن يكون بريد إلكتروني صحيح وفريد</small>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">كلمة المرور</label>
                                <input type="password" name="password" class="form-control" 
                                       {{ 'required' if not user else '' }}>
                                <div class="form-text">
                                    <small>
                                        {% if user %}
                                            اتركها فارغة إذا كنت لا تريد تغيير كلمة المرور
                                        {% else %}
                                            يجب أن تكون أكثر من 6 أحرف
                                        {% endif %}
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Permissions -->
                        <div class="col-md-6">
                            <h5 class="mb-3"><i class="fas fa-key"></i> الصلاحيات</h5>
                            
                            <div class="mb-3">
                                <label class="form-label">الدور</label>
                                <select name="role" class="form-select" required>
                                    <option value="">اختر الدور</option>
                                    <option value="user" {{ 'selected' if user and user.role == 'user' else '' }}>
                                        مستخدم عادي
                                    </option>
                                    <option value="admin" {{ 'selected' if user and user.role == 'admin' else '' }}>
                                        مدير النظام
                                    </option>
                                </select>
                                <div class="form-text">
                                    <small>
                                        <strong>مستخدم عادي:</strong> يمكنه إدارة العمال والرواتب<br>
                                        <strong>مدير النظام:</strong> صلاحيات كاملة + إدارة المستخدمين
                                    </small>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input type="checkbox" name="is_active" class="form-check-input" 
                                           {{ 'checked' if not user or user.is_active else '' }}>
                                    <label class="form-check-label">حساب نشط</label>
                                </div>
                                <div class="form-text">
                                    <small>المستخدمين غير النشطين لا يمكنهم تسجيل الدخول</small>
                                </div>
                            </div>
                            
                            {% if user %}
                            <div class="mb-3">
                                <label class="form-label">معلومات إضافية</label>
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <small>
                                            <strong>تاريخ الإنشاء:</strong> {{ user.created_at.strftime('%Y-%m-%d %H:%M') }}<br>
                                            <strong>آخر تحديث:</strong> الآن
                                        </small>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <hr>
                    
                    <!-- Form Actions -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="{{ url_for('users') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> {{ 'تحديث' if user else 'حفظ' }} المستخدم
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Password strength indicator
document.querySelector('input[name="password"]').addEventListener('input', function(e) {
    const password = e.target.value;
    const strength = document.getElementById('password-strength') || createStrengthIndicator(e.target);
    
    if (password.length === 0) {
        strength.style.display = 'none';
        return;
    }
    
    strength.style.display = 'block';
    
    let score = 0;
    if (password.length >= 6) score++;
    if (password.length >= 8) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[0-9]/.test(password)) score++;
    if (/[^A-Za-z0-9]/.test(password)) score++;
    
    const colors = ['danger', 'warning', 'info', 'success', 'success'];
    const texts = ['ضعيف جداً', 'ضعيف', 'متوسط', 'قوي', 'قوي جداً'];
    
    strength.className = `alert alert-${colors[score]} mt-2`;
    strength.textContent = `قوة كلمة المرور: ${texts[score]}`;
});

function createStrengthIndicator(input) {
    const indicator = document.createElement('div');
    indicator.id = 'password-strength';
    indicator.style.display = 'none';
    input.parentNode.appendChild(indicator);
    return indicator;
}
</script>
{% endblock %}

#!/usr/bin/env python3
"""
Create a portable version without default user
"""

import os
import shutil
import zipfile
from pathlib import Path

def create_no_default_user_portable():
    """Create portable version without default user"""
    
    print("🚀 إنشاء النسخة المحمولة بدون مستخدم افتراضي...")
    
    # Create directory
    portable_dir = "Qatar_Salary_System_No_Default_User"
    if os.path.exists(portable_dir):
        shutil.rmtree(portable_dir)
    os.makedirs(portable_dir)
    
    # Copy application files
    copy_application_files(portable_dir)
    
    # Copy icons
    copy_icons(portable_dir)
    
    # Create enhanced launchers
    create_enhanced_launchers(portable_dir)
    
    # Create desktop shortcut scripts
    create_desktop_shortcuts(portable_dir)
    
    # Create documentation
    create_documentation(portable_dir)
    
    # Create autorun files
    create_autorun_files(portable_dir)
    
    # Test the application
    test_application(portable_dir)
    
    # Create ZIP package
    create_zip_package(portable_dir)
    
    print("🎉 تم إنشاء النسخة المحمولة بدون مستخدم افتراضي بنجاح!")

def copy_application_files(portable_dir):
    """Copy all application files"""
    
    print("📁 نسخ ملفات التطبيق...")
    
    # Files to copy
    files_to_copy = [
        "simple_app.py",
        "requirements.txt"
    ]
    
    # Directories to copy
    dirs_to_copy = [
        "templates",
        "static"
    ]
    
    # Copy files
    for file in files_to_copy:
        if os.path.exists(file):
            shutil.copy2(file, portable_dir)
            print(f"   ✅ تم نسخ: {file}")
        else:
            print(f"   ⚠️ الملف غير موجود: {file}")
    
    # Copy directories
    for dir_name in dirs_to_copy:
        if os.path.exists(dir_name):
            dest_dir = os.path.join(portable_dir, dir_name)
            if os.path.exists(dest_dir):
                shutil.rmtree(dest_dir)
            shutil.copytree(dir_name, dest_dir)
            print(f"   ✅ تم نسخ: {dir_name}")
        else:
            print(f"   ⚠️ المجلد غير موجود: {dir_name}")

def copy_icons(portable_dir):
    """Copy icon files"""
    
    print("🖼️ نسخ الأيقونات...")
    
    # Icon files to copy
    icon_sources = [
        ("Qatar_Salary_System_Portable/qatar_salary_icon.ico", "qatar_salary_icon.ico"),
        ("Qatar_Salary_System_Portable/qatar_salary_icon.png", "qatar_salary_icon.png"),
        ("Qatar_Salary_System_Final_Portable/qatar_salary_icon.ico", "qatar_salary_icon.ico"),
        ("Qatar_Salary_System_Final_Portable/qatar_salary_icon.png", "qatar_salary_icon.png"),
        ("Qatar_Salary_System_Fixed_Portable/qatar_salary_icon.ico", "qatar_salary_icon.ico"),
        ("Qatar_Salary_System_Fixed_Portable/qatar_salary_icon.png", "qatar_salary_icon.png")
    ]
    
    icons_copied = False
    for source, dest_name in icon_sources:
        if os.path.exists(source):
            dest_file = os.path.join(portable_dir, dest_name)
            shutil.copy2(source, dest_file)
            print(f"   ✅ تم نسخ: {dest_name}")
            icons_copied = True
            break
    
    if not icons_copied:
        print("   ⚠️ لم يتم العثور على الأيقونات")

def create_enhanced_launchers(portable_dir):
    """Create enhanced launcher scripts"""
    
    print("🚀 إنشاء ملفات التشغيل...")
    
    # Enhanced Windows launcher
    enhanced_launcher = """@echo off
chcp 65001 >nul 2>&1
title 🇶🇦 نظام إدارة الرواتب القطري - بدون مستخدم افتراضي
color 0B

cls
echo.
echo        ╔═══════════════════════════════════════════════════════════╗
echo        ║  🇶🇦        نظام إدارة الرواتب القطري        🇶🇦  ║
echo        ║           Qatar Salary Management System              ║
echo        ║                بدون مستخدم افتراضي                   ║
echo        ╚═══════════════════════════════════════════════════════════╝
echo.
echo                    🚀 تشغيل آمن - بدون أخطاء 🚀
echo.
echo        ✅ لا يوجد مستخدم افتراضي
echo        ✅ يجب إنشاء حساب جديد
echo        ✅ أول مستخدم يصبح مدير تلقائياً
echo        ✅ أمان عالي ومحسن
echo.

REM Check if Python is installed
echo 🔍 فحص Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير موجود
    echo.
    echo 📥 يرجى تثبيت Python من: https://python.org
    echo.
    pause
    start https://python.org
    exit /b 1
)

echo ✅ Python موجود

REM Check and install requirements
echo 📦 فحص المكتبات...
python -c "import flask, flask_sqlalchemy, werkzeug" >nul 2>&1
if errorlevel 1 (
    echo 🔄 تثبيت المكتبات المطلوبة...
    echo    هذا قد يستغرق دقيقة أو دقيقتين...
    echo.
    
    python -m pip install --upgrade pip >nul 2>&1
    python -m pip install flask flask-sqlalchemy werkzeug >nul 2>&1
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المكتبات
        echo 🌐 تأكد من الاتصال بالإنترنت
        pause
        exit /b 1
    )
    
    echo ✅ تم تثبيت جميع المكتبات بنجاح
) else (
    echo ✅ جميع المكتبات موجودة
)

echo.
echo ┌──────────────────────────────────────────────────────────────┐
echo │  🚀 جاري تشغيل النظام...                                     │
echo │                                                              │
echo │  🌐 العنوان: http://localhost:7474                          │
echo │  👤 لا يوجد مستخدم افتراضي                                  │
echo │  🆕 يجب إنشاء حساب جديد                                     │
echo │  👑 أول مستخدم يصبح مدير                                    │
echo │                                                              │
echo │  ✨ الميزات:                                                │
echo │     • أمان عالي بدون مستخدم افتراضي                        │
echo │     • إدارة العمال مع تحديد الجنس                          │
echo │     • شريط متحرك للرواتب                                   │
echo │     • تقارير مفصلة ومتطورة                                 │
echo │                                                              │
echo │  🛑 للإيقاف اضغط Ctrl+C                                     │
echo └──────────────────────────────────────────────────────────────┘
echo.

REM Test the application first
echo 🧪 اختبار التطبيق...
python -c "import simple_app; print('✅ التطبيق جاهز للتشغيل')" 2>nul
if errorlevel 1 (
    echo ❌ خطأ في التطبيق
    echo 📝 تفاصيل الخطأ:
    python -c "import simple_app"
    echo.
    pause
    exit /b 1
)

REM Open browser after 3 seconds
start /min cmd /c "timeout /t 3 /nobreak >nul && start http://localhost:7474"

REM Start the application
python simple_app.py

echo.
echo ┌──────────────────────────────────────────────────────────────┐
echo │  ✅ تم إيقاف النظام بنجاح                                    │
echo │  🙏 شكراً لاستخدام نظام إدارة الرواتب القطري               │
echo │  🔒 النسخة الآمنة - بدون مستخدم افتراضي                    │
echo └──────────────────────────────────────────────────────────────┘
echo.
pause
"""
    
    with open(os.path.join(portable_dir, "🚀_تشغيل_آمن.bat"), "w", encoding="utf-8") as f:
        f.write(enhanced_launcher)
    
    print("   ✅ تم إنشاء ملف التشغيل الآمن")

    # Quick launcher
    quick_launcher = """@echo off
chcp 65001 >nul
title نظام الرواتب القطري - النسخة الآمنة

echo 🇶🇦 نظام إدارة الرواتب القطري 🇶🇦
echo النسخة الآمنة - بدون مستخدم افتراضي
echo.

python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير موجود - شغل "🚀_تشغيل_آمن.bat"
    pause
    exit
)

python -c "import flask" >nul 2>&1
if errorlevel 1 (
    echo 📦 تثبيت المكتبات...
    pip install flask flask-sqlalchemy werkzeug >nul
)

echo ✅ جاري التشغيل...
echo 🌐 http://localhost:7474
echo 🆕 يجب إنشاء حساب جديد

start http://localhost:7474
python simple_app.py
pause
"""

    with open(os.path.join(portable_dir, "تشغيل_سريع.bat"), "w", encoding="utf-8") as f:
        f.write(quick_launcher)

def create_desktop_shortcuts(portable_dir):
    """Create desktop shortcut scripts"""

    print("🔗 إنشاء سكريپتات الاختصارات...")

    # Windows desktop shortcut
    shortcut_script = """@echo off
chcp 65001 >nul
title إنشاء اختصار سطح المكتب - النسخة الآمنة

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║            إنشاء اختصار على سطح المكتب - النسخة الآمنة        ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

set "current_dir=%~dp0"
set "desktop=%USERPROFILE%\\Desktop"
set "shortcut_name=🇶🇦 نظام الرواتب القطري (آمن).lnk"

echo 🔧 جاري إنشاء الاختصار...

REM Create VBS script
echo Set oWS = WScript.CreateObject("WScript.Shell") > temp_shortcut.vbs
echo sLinkFile = "%desktop%\\%shortcut_name%" >> temp_shortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> temp_shortcut.vbs
echo oLink.TargetPath = "%current_dir%🚀_تشغيل_آمن.bat" >> temp_shortcut.vbs
echo oLink.WorkingDirectory = "%current_dir%" >> temp_shortcut.vbs
if exist "%current_dir%qatar_salary_icon.ico" (
    echo oLink.IconLocation = "%current_dir%qatar_salary_icon.ico" >> temp_shortcut.vbs
)
echo oLink.Description = "نظام إدارة الرواتب القطري - النسخة الآمنة بدون مستخدم افتراضي" >> temp_shortcut.vbs
echo oLink.Save >> temp_shortcut.vbs

REM Execute VBS script
cscript //nologo temp_shortcut.vbs

REM Clean up
del temp_shortcut.vbs

if exist "%desktop%\\%shortcut_name%" (
    echo ✅ تم إنشاء الاختصار بنجاح على سطح المكتب!
    echo 🎯 يمكنك الآن تشغيل النظام من سطح المكتب
    echo 🔒 النسخة الآمنة - بدون مستخدم افتراضي
    echo 🆕 يجب إنشاء حساب جديد عند أول استخدام
) else (
    echo ❌ فشل في إنشاء الاختصار
)

echo.
pause
"""

    with open(os.path.join(portable_dir, "إنشاء_اختصار_سطح_المكتب.bat"), "w", encoding="utf-8") as f:
        f.write(shortcut_script)

    print("   ✅ تم إنشاء سكريپت اختصار Windows")

def create_documentation(portable_dir):
    """Create comprehensive documentation"""

    print("📖 إنشاء التوثيق...")

    # Main README
    readme_content = """# نظام إدارة الرواتب القطري - النسخة الآمنة
# Qatar Salary Management System - Secure Version

## 🔒 النسخة الآمنة - بدون مستخدم افتراضي

### ✅ مميزات الأمان:
- لا يوجد مستخدم افتراضي (admin/admin123)
- يجب إنشاء حساب جديد لكل مستخدم
- أول مستخدم يصبح مدير تلقائياً
- كلمات مرور قوية مطلوبة
- تشفير كامل لكلمات المرور

### 🚀 أول استخدام:
1. شغل النظام
2. ستظهر صفحة إنشاء حساب جديد
3. أدخل بياناتك (اسم المستخدم، البريد، كلمة المرور)
4. ستحصل على صلاحيات المدير تلقائياً
5. يمكنك إضافة مستخدمين آخرين لاحقاً

### 🖱️ طرق التشغيل:

#### Windows:
1. **التشغيل الآمن:** 🚀_تشغيل_آمن.bat
2. **التشغيل السريع:** تشغيل_سريع.bat
3. **إنشاء اختصار:** إنشاء_اختصار_سطح_المكتب.bat

### 🌐 الوصول للنظام:
العنوان: http://localhost:7474

### 🔐 إنشاء الحساب الأول:
- لا يوجد مستخدم افتراضي
- يجب إنشاء حساب جديد
- أول مستخدم = مدير النظام
- كلمة مرور قوية مطلوبة (أكثر من 6 أحرف)

### 📋 المتطلبات:
- Python 3.7+ مثبت على النظام
- اتصال إنترنت (أول مرة فقط)
- 100 MB مساحة فارغة
- 512 MB ذاكرة عشوائية

### ✨ الميزات الكاملة:
✅ إدارة العمال مع تحديد الجنس (ذكر/أنثى)
✅ حساب الرواتب تلقائياً مع المكافآت والخصومات
✅ تقارير مفصلة وإحصائيات متقدمة
✅ شريط متحرك للرواتب مع أيقونات الجنس
✅ واجهة عربية جميلة ومتجاوبة
✅ نظام مستخدمين آمن ومحسن
✅ تشفير كامل للبيانات الحساسة

### 🛠️ استكشاف الأخطاء:
1. تأكد من تثبيت Python
2. تأكد من الاتصال بالإنترنت
3. شغل كمدير (Run as Administrator)
4. استخدم التشغيل الآمن للحصول على رسائل مفصلة

### 🎯 الجديد في النسخة الآمنة:
✅ إزالة المستخدم الافتراضي نهائياً
✅ نظام تسجيل مستخدمين محسن
✅ أمان عالي مع كلمات مرور قوية
✅ أول مستخدم يصبح مدير تلقائياً
✅ واجهة تسجيل جميلة ومتطورة
✅ حماية شاملة للبيانات

---
🇶🇦 صنع بحب في قطر - النسخة الآمنة بدون مستخدم افتراضي
"""

    with open(os.path.join(portable_dir, "README_SECURE.md"), "w", encoding="utf-8") as f:
        f.write(readme_content)

    print("   ✅ تم إنشاء التوثيق الرئيسي")

def create_autorun_files(portable_dir):
    """Create autorun files for USB drives"""

    print("💿 إنشاء ملفات التشغيل التلقائي...")

    # Autorun.inf for Windows
    autorun_content = """[autorun]
icon=qatar_salary_icon.ico
label=نظام إدارة الرواتب القطري (آمن)
action=تشغيل نظام إدارة الرواتب القطري - النسخة الآمنة
open=🚀_تشغيل_آمن.bat
"""

    with open(os.path.join(portable_dir, "autorun.inf"), "w", encoding="utf-8") as f:
        f.write(autorun_content)

    print("   ✅ تم إنشاء ملف التشغيل التلقائي")

def test_application(portable_dir):
    """Test the application for errors"""

    print("🧪 اختبار التطبيق...")

    try:
        # Change to the portable directory
        original_dir = os.getcwd()
        os.chdir(portable_dir)

        # Try to import the application
        import sys
        sys.path.insert(0, '.')

        try:
            import simple_app
            print("   ✅ تم تحميل التطبيق بنجاح")

            # Test database models
            try:
                with simple_app.app.app_context():
                    simple_app.db.create_all()
                    print("   ✅ قاعدة البيانات جاهزة")

                    # Check that no default user exists
                    user_count = simple_app.User.query.count()
                    if user_count == 0:
                        print("   ✅ لا يوجد مستخدم افتراضي - النظام آمن")
                    else:
                        print(f"   ⚠️ يوجد {user_count} مستخدم في قاعدة البيانات")

            except Exception as e:
                print(f"   ⚠️ تحذير في قاعدة البيانات: {e}")

        except Exception as e:
            print(f"   ❌ خطأ في التطبيق: {e}")
            return False

        finally:
            # Restore original directory
            os.chdir(original_dir)
            # Remove from sys.path
            if '.' in sys.path:
                sys.path.remove('.')

        return True

    except Exception as e:
        print(f"   ❌ خطأ في الاختبار: {e}")
        return False

def create_zip_package(portable_dir):
    """Create ZIP package"""

    print("📦 إنشاء الملف المضغوط...")

    zip_filename = f"{portable_dir}.zip"

    # Remove existing ZIP if exists
    if os.path.exists(zip_filename):
        os.remove(zip_filename)

    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(portable_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, portable_dir)
                zipf.write(file_path, arc_name)

    # Get ZIP size
    zip_size = os.path.getsize(zip_filename) / (1024 * 1024)
    print(f"   📊 حجم الملف المضغوط: {zip_size:.2f} MB")

    return zip_filename

if __name__ == "__main__":
    create_no_default_user_portable()

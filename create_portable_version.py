#!/usr/bin/env python3
"""
Scrip<PERSON> to create a portable version of the Qatar Salary Management System
"""

import os
import shutil
import zipfile
from pathlib import Path

def create_portable_version():
    """Create a portable version that can run from USB/Flash drive"""
    
    print("🚀 إنشاء النسخة المحمولة لنظام إدارة الرواتب القطري...")
    
    # Create portable directory
    portable_dir = "Qatar_Salary_System_Portable"
    if os.path.exists(portable_dir):
        shutil.rmtree(portable_dir)
    os.makedirs(portable_dir)
    
    # Files and directories to include
    files_to_copy = [
        "simple_app.py",
        "requirements.txt",
        "templates/",
        "static/",
        "README.md"
    ]
    
    # Copy files
    print("📁 نسخ الملفات...")
    for item in files_to_copy:
        if os.path.exists(item):
            if os.path.isdir(item):
                shutil.copytree(item, os.path.join(portable_dir, item))
                print(f"   ✅ تم نسخ المجلد: {item}")
            else:
                shutil.copy2(item, portable_dir)
                print(f"   ✅ تم نسخ الملف: {item}")
        else:
            print(f"   ⚠️  الملف غير موجود: {item}")
    
    # Create batch files for Windows
    create_windows_batch_files(portable_dir)
    
    # Create shell scripts for Linux/Mac
    create_unix_scripts(portable_dir)
    
    # Create README for portable version
    create_portable_readme(portable_dir)
    
    # Create requirements for portable version
    create_portable_requirements(portable_dir)
    
    # Create ZIP file
    create_zip_package(portable_dir)
    
    print(f"\n✅ تم إنشاء النسخة المحمولة في المجلد: {portable_dir}")
    print(f"📦 تم إنشاء ملف مضغوط: {portable_dir}.zip")
    print("\n🎉 النسخة المحمولة جاهزة للاستخدام!")

def create_windows_batch_files(portable_dir):
    """Create Windows batch files"""
    print("🪟 إنشاء ملفات Windows...")
    
    # Install script
    install_bat = """@echo off
echo ========================================
echo    نظام إدارة الرواتب القطري
echo    Qatar Salary Management System
echo ========================================
echo.
echo جاري تثبيت المتطلبات...
echo Installing requirements...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo ❌ Python is not installed
    echo.
    echo يرجى تثبيت Python من: https://python.org
    echo Please install Python from: https://python.org
    pause
    exit /b 1
)

REM Install requirements
pip install -r requirements.txt

if errorlevel 1 (
    echo ❌ فشل في تثبيت المتطلبات
    echo ❌ Failed to install requirements
    pause
    exit /b 1
)

echo.
echo ✅ تم تثبيت المتطلبات بنجاح
echo ✅ Requirements installed successfully
echo.
echo يمكنك الآن تشغيل النظام باستخدام: run.bat
echo You can now run the system using: run.bat
echo.
pause
"""
    
    # Run script
    run_bat = """@echo off
echo ========================================
echo    نظام إدارة الرواتب القطري
echo    Qatar Salary Management System
echo ========================================
echo.
echo جاري تشغيل النظام...
echo Starting the system...
echo.
echo سيتم فتح النظام في المتصفح على العنوان:
echo The system will open in browser at:
echo http://localhost:7474
echo.
echo للإيقاف اضغط Ctrl+C
echo To stop press Ctrl+C
echo.

python simple_app.py

echo.
echo تم إيقاف النظام
echo System stopped
pause
"""
    
    with open(os.path.join(portable_dir, "install.bat"), "w", encoding="utf-8") as f:
        f.write(install_bat)
    
    with open(os.path.join(portable_dir, "run.bat"), "w", encoding="utf-8") as f:
        f.write(run_bat)
    
    print("   ✅ تم إنشاء install.bat")
    print("   ✅ تم إنشاء run.bat")

def create_unix_scripts(portable_dir):
    """Create Unix shell scripts"""
    print("🐧 إنشاء ملفات Unix/Linux/Mac...")
    
    # Install script
    install_sh = """#!/bin/bash
echo "========================================"
echo "   نظام إدارة الرواتب القطري"
echo "   Qatar Salary Management System"
echo "========================================"
echo ""
echo "جاري تثبيت المتطلبات..."
echo "Installing requirements..."
echo ""

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ Python غير مثبت على النظام"
        echo "❌ Python is not installed"
        echo ""
        echo "يرجى تثبيت Python من: https://python.org"
        echo "Please install Python from: https://python.org"
        exit 1
    fi
    PYTHON_CMD="python"
else
    PYTHON_CMD="python3"
fi

# Install requirements
$PYTHON_CMD -m pip install -r requirements.txt

if [ $? -ne 0 ]; then
    echo "❌ فشل في تثبيت المتطلبات"
    echo "❌ Failed to install requirements"
    exit 1
fi

echo ""
echo "✅ تم تثبيت المتطلبات بنجاح"
echo "✅ Requirements installed successfully"
echo ""
echo "يمكنك الآن تشغيل النظام باستخدام: ./run.sh"
echo "You can now run the system using: ./run.sh"
echo ""
"""
    
    # Run script
    run_sh = """#!/bin/bash
echo "========================================"
echo "   نظام إدارة الرواتب القطري"
echo "   Qatar Salary Management System"
echo "========================================"
echo ""
echo "جاري تشغيل النظام..."
echo "Starting the system..."
echo ""
echo "سيتم فتح النظام في المتصفح على العنوان:"
echo "The system will open in browser at:"
echo "http://localhost:7474"
echo ""
echo "للإيقاف اضغط Ctrl+C"
echo "To stop press Ctrl+C"
echo ""

# Check if Python is installed
if command -v python3 &> /dev/null; then
    python3 simple_app.py
elif command -v python &> /dev/null; then
    python simple_app.py
else
    echo "❌ Python غير مثبت على النظام"
    echo "❌ Python is not installed"
    exit 1
fi

echo ""
echo "تم إيقاف النظام"
echo "System stopped"
"""
    
    install_path = os.path.join(portable_dir, "install.sh")
    run_path = os.path.join(portable_dir, "run.sh")
    
    with open(install_path, "w", encoding="utf-8") as f:
        f.write(install_sh)
    
    with open(run_path, "w", encoding="utf-8") as f:
        f.write(run_sh)
    
    # Make scripts executable
    try:
        os.chmod(install_path, 0o755)
        os.chmod(run_path, 0o755)
    except:
        pass  # Windows doesn't support chmod
    
    print("   ✅ تم إنشاء install.sh")
    print("   ✅ تم إنشاء run.sh")

def create_portable_readme(portable_dir):
    """Create README for portable version"""
    print("📖 إنشاء ملف README...")
    
    readme_content = """# نظام إدارة الرواتب القطري - النسخة المحمولة
# Qatar Salary Management System - Portable Version

## 🚀 التشغيل السريع / Quick Start

### Windows:
1. تشغيل `install.bat` لتثبيت المتطلبات (مرة واحدة فقط)
2. تشغيل `run.bat` لبدء النظام
3. فتح المتصفح على: http://localhost:7474

### Linux/Mac:
1. تشغيل `./install.sh` لتثبيت المتطلبات (مرة واحدة فقط)
2. تشغيل `./run.sh` لبدء النظام
3. فتح المتصفح على: http://localhost:7474

## 📋 المتطلبات / Requirements

- Python 3.7+ مثبت على النظام
- اتصال بالإنترنت لتثبيت المكتبات (أول مرة فقط)

## 🔐 بيانات الدخول الافتراضية / Default Login

- **اسم المستخدم / Username:** admin
- **كلمة المرور / Password:** admin123

## 📁 محتويات النسخة المحمولة / Portable Contents

- `simple_app.py` - التطبيق الرئيسي
- `templates/` - قوالب الواجهة
- `static/` - الملفات الثابتة (CSS, JS, Images)
- `requirements.txt` - المكتبات المطلوبة
- `install.bat/sh` - سكريپت التثبيت
- `run.bat/sh` - سكريپت التشغيل

## ✨ الميزات / Features

- ✅ إدارة العمال والموظفين
- ✅ حساب وإدارة الرواتب
- ✅ تقارير مفصلة
- ✅ واجهة عربية/إنجليزية
- ✅ نظام مستخدمين متعدد
- ✅ تصدير البيانات
- ✅ شريط متحرك للرواتب
- ✅ تصنيف حسب الجنس

## 🛠️ استكشاف الأخطاء / Troubleshooting

### إذا لم يعمل النظام:
1. تأكد من تثبيت Python
2. شغل install.bat/sh مرة أخرى
3. تأكد من الاتصال بالإنترنت

### If the system doesn't work:
1. Make sure Python is installed
2. Run install.bat/sh again
3. Check internet connection

## 📞 الدعم / Support

للدعم والمساعدة، يرجى التواصل مع فريق التطوير.
For support and help, please contact the development team.

## 📄 الترخيص / License

هذا النظام مطور خصيصاً لإدارة الرواتب في قطر.
This system is specially developed for salary management in Qatar.

---
🇶🇦 صنع بحب في قطر / Made with ❤️ in Qatar
"""
    
    with open(os.path.join(portable_dir, "README_PORTABLE.md"), "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print("   ✅ تم إنشاء README_PORTABLE.md")

def create_portable_requirements(portable_dir):
    """Create optimized requirements for portable version"""
    print("📦 إنشاء ملف المتطلبات...")
    
    # Copy existing requirements or create minimal one
    if os.path.exists("requirements.txt"):
        shutil.copy2("requirements.txt", portable_dir)
    else:
        # Create minimal requirements
        requirements = """Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Werkzeug==2.3.7
Jinja2==3.1.2
"""
        with open(os.path.join(portable_dir, "requirements.txt"), "w") as f:
            f.write(requirements)
    
    print("   ✅ تم إنشاء requirements.txt")

def create_zip_package(portable_dir):
    """Create ZIP package for easy distribution"""
    print("📦 إنشاء الملف المضغوط...")
    
    zip_filename = f"{portable_dir}.zip"
    
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(portable_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, portable_dir)
                zipf.write(file_path, arc_name)
    
    print(f"   ✅ تم إنشاء {zip_filename}")

if __name__ == "__main__":
    create_portable_version()

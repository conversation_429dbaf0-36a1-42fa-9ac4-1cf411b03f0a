#!/bin/bash

echo ""
echo "========================================"
echo "   نظام إدارة الرواتب القطري"
echo "   Qatar Salary Management System"
echo "========================================"
echo ""

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ Python غير مثبت على النظام"
        echo ""
        echo "يرجى تثبيت Python من: https://python.org"
        echo "ثم تشغيل ./install.sh أولاً"
        echo ""
        exit 1
    fi
    PYTHON_CMD="python"
else
    PYTHON_CMD="python3"
fi

# Check if requirements are installed
$PYTHON_CMD -c "import flask" &> /dev/null
if [ $? -ne 0 ]; then
    echo "📦 جاري تثبيت المتطلبات..."
    $PYTHON_CMD -m pip install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "❌ فشل في تثبيت المتطلبات"
        echo "يرجى تشغيل ./install.sh أولاً"
        exit 1
    fi
fi

echo "✅ جاري تشغيل النظام..."
echo ""
echo "🌐 سيتم فتح النظام في المتصفح على:"
echo "   http://localhost:7474"
echo ""
echo "🔐 بيانات الدخول:"
echo "   اسم المستخدم: admin"
echo "   كلمة المرور: admin123"
echo ""
echo "🛑 للإيقاف اضغط Ctrl+C"
echo ""

# Start the application
$PYTHON_CMD simple_app.py

echo ""
echo "تم إيقاف النظام"

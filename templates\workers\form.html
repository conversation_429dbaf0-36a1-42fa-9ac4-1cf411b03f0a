{% extends "base.html" %}

{% block title %}{{ title }} - Salary System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-user-{{ 'plus' if 'Add' in title else 'edit' }}"></i> {{ title }}</h1>
            <a href="{{ url_for('workers') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Workers
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <h5 class="mb-3"><i class="fas fa-user"></i> Basic Information</h5>
                            
                            <div class="mb-3">
                                {{ form.name.label(class="form-label") }}
                                {{ form.name(class="form-control" + (" is-invalid" if form.name.errors else "")) }}
                                {% if form.name.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.name.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                {{ form.nationality.label(class="form-label") }}
                                {{ form.nationality(class="form-control" + (" is-invalid" if form.nationality.errors else "")) }}
                                {% if form.nationality.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.nationality.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                {{ form.id_number.label(class="form-label") }}
                                {{ form.id_number(class="form-control" + (" is-invalid" if form.id_number.errors else "")) }}
                                {% if form.id_number.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.id_number.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                {{ form.job_type.label(class="form-label") }}
                                {{ form.job_type(class="form-select" + (" is-invalid" if form.job_type.errors else "")) }}
                                {% if form.job_type.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.job_type.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- Employment Details -->
                        <div class="col-md-6">
                            <h5 class="mb-3"><i class="fas fa-briefcase"></i> Employment Details</h5>
                            
                            <div class="mb-3">
                                {{ form.employment_date.label(class="form-label") }}
                                {{ form.employment_date(class="form-control" + (" is-invalid" if form.employment_date.errors else "")) }}
                                {% if form.employment_date.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.employment_date.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                {{ form.monthly_salary.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.monthly_salary(class="form-control" + (" is-invalid" if form.monthly_salary.errors else "")) }}
                                    <span class="input-group-text">SAR</span>
                                </div>
                                {% if form.monthly_salary.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.monthly_salary.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    {{ form.is_active(class="form-check-input") }}
                                    {{ form.is_active.label(class="form-check-label") }}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <!-- File Uploads -->
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="mb-3"><i class="fas fa-camera"></i> Photo</h5>
                            <div class="mb-3">
                                {{ form.photo.label(class="form-label") }}
                                {{ form.photo(class="form-control" + (" is-invalid" if form.photo.errors else "")) }}
                                {% if form.photo.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.photo.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">Accepted formats: JPG, PNG, GIF (Max 16MB)</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h5 class="mb-3"><i class="fas fa-file-alt"></i> Documents</h5>
                            <div class="mb-3">
                                {{ form.documents.label(class="form-label") }}
                                {{ form.documents(class="form-control" + (" is-invalid" if form.documents.errors else "")) }}
                                {% if form.documents.errors %}
                                    <div class="invalid-feedback">
                                        {% for error in form.documents.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-text">Accepted formats: PDF, DOC, DOCX, JPG, PNG (Max 16MB)</div>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <!-- Form Actions -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="{{ url_for('workers') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> {{ 'Update' if 'Edit' in title else 'Save' }} Worker
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Preview uploaded photo
document.querySelector('input[name="photo"]').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            // Create or update preview image
            let preview = document.getElementById('photo-preview');
            if (!preview) {
                preview = document.createElement('img');
                preview.id = 'photo-preview';
                preview.className = 'img-thumbnail mt-2';
                preview.style.maxWidth = '150px';
                e.target.parentNode.appendChild(preview);
            }
            preview.src = e.target.result;
        };
        reader.readAsDataURL(file);
    }
});

// Set today's date as default for employment date if adding new worker
{% if 'Add' in title %}
document.addEventListener('DOMContentLoaded', function() {
    const employmentDateField = document.querySelector('input[name="employment_date"]');
    if (!employmentDateField.value) {
        employmentDateField.value = new Date().toISOString().split('T')[0];
    }
});
{% endif %}
</script>
{% endblock %}

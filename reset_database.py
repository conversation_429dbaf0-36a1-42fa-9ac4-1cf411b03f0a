#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to reset the database with gender column
"""

import os
import sqlite3

def reset_database():
    """Delete and recreate database"""
    db_files = ['qatar_salary_system.db', 'instance/qatar_salary_system.db']
    
    for db_file in db_files:
        if os.path.exists(db_file):
            try:
                os.remove(db_file)
                print(f"Deleted {db_file}")
            except Exception as e:
                print(f"Error deleting {db_file}: {e}")
    
    print("Database files deleted. The app will create a new database with gender column on next run.")

if __name__ == "__main__":
    reset_database()

#!/usr/bin/env python3
"""
Create a standalone executable version that runs directly without Python
"""

import os
import sys
import subprocess
import shutil
import zipfile
from pathlib import Path

def install_pyinstaller():
    """Install PyInstaller if not available"""
    try:
        import PyInstaller
        print("✅ PyInstaller موجود")
        return True
    except ImportError:
        print("📦 تثبيت PyInstaller...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✅ تم تثبيت PyInstaller بنجاح")
            return True
        except Exception as e:
            print(f"❌ فشل في تثبيت PyInstaller: {e}")
            return False

def create_spec_file():
    """Create PyInstaller spec file for the application"""
    
    spec_content = """# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# Add all template and static files
datas = []

# Add templates directory
import os
for root, dirs, files in os.walk('templates'):
    for file in files:
        file_path = os.path.join(root, file)
        datas.append((file_path, root))

# Add static directory if exists
if os.path.exists('static'):
    for root, dirs, files in os.walk('static'):
        for file in files:
            file_path = os.path.join(root, file)
            datas.append((file_path, root))

a = Analysis(
    ['simple_app.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=[
        'flask',
        'flask_sqlalchemy',
        'werkzeug',
        'werkzeug.security',
        'jinja2',
        'sqlite3',
        'datetime',
        'os',
        'hashlib',
        'functools',
        'sqlalchemy',
        'sqlalchemy.sql.default_comparator',
        'email_validator'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='نظام_إدارة_الرواتب_القطري',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='Qatar_Salary_System_Portable/qatar_salary_icon.ico' if os.path.exists('Qatar_Salary_System_Portable/qatar_salary_icon.ico') else None,
)
"""
    
    with open("standalone.spec", "w", encoding="utf-8") as f:
        f.write(spec_content)
    
    print("✅ تم إنشاء ملف التكوين")

def build_executable():
    """Build the standalone executable"""
    print("🔨 بناء الملف التنفيذي...")
    
    try:
        # Clean previous builds
        if os.path.exists("build"):
            shutil.rmtree("build")
        if os.path.exists("dist"):
            shutil.rmtree("dist")
        
        # Build the executable
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm",
            "standalone.spec"
        ]
        
        print("   🔄 جاري البناء... (قد يستغرق عدة دقائق)")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("   ✅ تم بناء الملف التنفيذي بنجاح")
            return True
        else:
            print(f"   ❌ خطأ في البناء:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"   ❌ خطأ: {e}")
        return False

def create_standalone_package():
    """Create the final standalone package"""
    print("📦 إنشاء الحزمة المستقلة...")
    
    # Create standalone directory
    standalone_dir = "Qatar_Salary_System_Standalone"
    if os.path.exists(standalone_dir):
        shutil.rmtree(standalone_dir)
    os.makedirs(standalone_dir)
    
    # Find the executable
    exe_name = "نظام_إدارة_الرواتب_القطري.exe" if os.name == 'nt' else "نظام_إدارة_الرواتب_القطري"
    exe_source = os.path.join("dist", exe_name)
    
    if not os.path.exists(exe_source):
        print(f"   ❌ الملف التنفيذي غير موجود: {exe_source}")
        return False
    
    # Copy executable
    exe_dest = os.path.join(standalone_dir, exe_name)
    shutil.copy2(exe_source, exe_dest)
    print(f"   ✅ تم نسخ الملف التنفيذي: {exe_name}")
    
    # Copy icon if exists
    icon_source = "Qatar_Salary_System_Portable/qatar_salary_icon.ico"
    if os.path.exists(icon_source):
        shutil.copy2(icon_source, standalone_dir)
        print("   ✅ تم نسخ الأيقونة")
    
    # Create launcher scripts
    create_launcher_scripts(standalone_dir, exe_name)
    
    # Create README
    create_standalone_readme(standalone_dir)
    
    # Create desktop shortcut script
    create_desktop_shortcut_script(standalone_dir, exe_name)
    
    # Create ZIP package
    create_standalone_zip(standalone_dir)
    
    # Get file size
    exe_size = os.path.getsize(exe_dest) / (1024 * 1024)
    print(f"   📊 حجم الملف التنفيذي: {exe_size:.1f} MB")
    
    return True

def create_launcher_scripts(standalone_dir, exe_name):
    """Create launcher scripts for easy execution"""
    
    # Windows launcher
    windows_launcher = f"""@echo off
chcp 65001 >nul
title 🇶🇦 نظام إدارة الرواتب القطري - النسخة المستقلة
color 0B

cls
echo.
echo        ╔═══════════════════════════════════════════════════════════╗
echo        ║  🇶🇦        نظام إدارة الرواتب القطري        🇶🇦  ║
echo        ║           Qatar Salary Management System              ║
echo        ║                   النسخة المستقلة                    ║
echo        ╚═══════════════════════════════════════════════════════════╝
echo.
echo                    🚀 تشغيل مباشر - بدون متطلبات 🚀
echo.
echo        ✅ لا تحتاج Python أو أي مكتبات
echo        ✅ تعمل مباشرة من الفلاش ميموري
echo        ✅ سرعة فائقة في التشغيل
echo.
echo        🌐 العنوان: http://localhost:7474
echo        👤 المستخدم: admin
echo        🔑 كلمة المرور: admin123
echo.
echo        🔄 جاري التشغيل...

REM Start and open browser
start http://localhost:7474
"{exe_name}"

echo.
echo        ✅ تم إيقاف النظام
pause
"""
    
    with open(os.path.join(standalone_dir, "🚀_تشغيل_مباشر.bat"), "w", encoding="utf-8") as f:
        f.write(windows_launcher)
    
    # Linux/Mac launcher
    unix_launcher = f"""#!/bin/bash

clear
echo ""
echo "    ╔═══════════════════════════════════════════════════════════╗"
echo "    ║  🇶🇦        نظام إدارة الرواتب القطري        🇶🇦  ║"
echo "    ║           Qatar Salary Management System              ║"
echo "    ║                   النسخة المستقلة                    ║"
echo "    ╚═══════════════════════════════════════════════════════════╝"
echo ""
echo "                🚀 تشغيل مباشر - بدون متطلبات 🚀"
echo ""
echo "    ✅ لا تحتاج Python أو أي مكتبات"
echo "    ✅ تعمل مباشرة من الفلاش ميموري"
echo "    ✅ سرعة فائقة في التشغيل"
echo ""
echo "    🌐 العنوان: http://localhost:7474"
echo "    👤 المستخدم: admin"
echo "    🔑 كلمة المرور: admin123"
echo ""
echo "    🔄 جاري التشغيل..."

# Open browser
if command -v xdg-open &> /dev/null; then
    xdg-open http://localhost:7474 &
elif command -v open &> /dev/null; then
    open http://localhost:7474 &
fi

# Run the application
./{exe_name}

echo ""
echo "    ✅ تم إيقاف النظام"
"""
    
    launcher_path = os.path.join(standalone_dir, "🚀_تشغيل_مباشر.sh")
    with open(launcher_path, "w", encoding="utf-8") as f:
        f.write(unix_launcher)
    
    # Make executable on Unix systems
    try:
        os.chmod(launcher_path, 0o755)
    except:
        pass
    
    print("   ✅ تم إنشاء ملفات التشغيل")

def create_standalone_readme(standalone_dir):
    """Create README for standalone version"""
    
    readme_content = """# نظام إدارة الرواتب القطري - النسخة المستقلة
# Qatar Salary Management System - Standalone Version

## 🚀 التشغيل المباشر - بدون متطلبات

### ✅ مميزات النسخة المستقلة:
- لا تحتاج تثبيت Python
- لا تحتاج تثبيت مكتبات إضافية
- لا تحتاج اتصال إنترنت
- تعمل مباشرة من الفلاش ميموري
- سرعة فائقة في التشغيل
- حجم محسن ومضغوط

### 🖱️ طريقة التشغيل:

#### Windows:
اضغط مرتين على: 🚀_تشغيل_مباشر.bat

#### Linux/Mac:
اضغط مرتين على: 🚀_تشغيل_مباشر.sh
أو في Terminal: ./🚀_تشغيل_مباشر.sh

#### تشغيل مباشر:
اضغط مرتين على الملف التنفيذي مباشرة

### 🌐 الوصول للنظام:
العنوان: http://localhost:7474

### 🔐 بيانات الدخول:
اسم المستخدم: admin
كلمة المرور: admin123

### 📋 متطلبات النظام:
- Windows 7+ / Linux / macOS
- 100 MB مساحة فارغة
- 512 MB ذاكرة عشوائية
- لا تحتاج Python أو مكتبات

### ✨ الميزات:
✅ إدارة العمال مع تحديد الجنس
✅ حساب الرواتب تلقائياً
✅ تقارير مفصلة
✅ شريط متحرك للرواتب
✅ واجهة عربية جميلة
✅ تصميم متجاوب

### 🛠️ استكشاف الأخطاء:
- تأكد من صلاحيات التشغيل
- أغلق برامج مكافحة الفيروسات مؤقتاً
- شغل كمدير (Run as Administrator)

### 🎯 إنشاء اختصار سطح المكتب:
شغل: إنشاء_اختصار_سطح_المكتب.bat

---
🇶🇦 صنع بحب في قطر
"""
    
    with open(os.path.join(standalone_dir, "README_STANDALONE.md"), "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print("   ✅ تم إنشاء ملف README")

def create_desktop_shortcut_script(standalone_dir, exe_name):
    """Create desktop shortcut creation script"""
    
    shortcut_script = f"""@echo off
chcp 65001 >nul
title إنشاء اختصار سطح المكتب - النسخة المستقلة

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║            إنشاء اختصار على سطح المكتب - النسخة المستقلة      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

set "current_dir=%~dp0"
set "desktop=%USERPROFILE%\\Desktop"
set "shortcut_name=🇶🇦 نظام الرواتب القطري (مستقل).lnk"

echo 🔧 جاري إنشاء الاختصار...

REM Create VBS script
echo Set oWS = WScript.CreateObject("WScript.Shell") > temp_shortcut.vbs
echo sLinkFile = "%desktop%\\%shortcut_name%" >> temp_shortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> temp_shortcut.vbs
echo oLink.TargetPath = "%current_dir%{exe_name}" >> temp_shortcut.vbs
echo oLink.WorkingDirectory = "%current_dir%" >> temp_shortcut.vbs
echo oLink.IconLocation = "%current_dir%qatar_salary_icon.ico" >> temp_shortcut.vbs
echo oLink.Description = "نظام إدارة الرواتب القطري - النسخة المستقلة" >> temp_shortcut.vbs
echo oLink.Save >> temp_shortcut.vbs

REM Execute VBS script
cscript //nologo temp_shortcut.vbs

REM Clean up
del temp_shortcut.vbs

if exist "%desktop%\\%shortcut_name%" (
    echo ✅ تم إنشاء الاختصار بنجاح على سطح المكتب!
    echo 🎯 يمكنك الآن تشغيل النظام من سطح المكتب
    echo 🚀 النسخة المستقلة - لا تحتاج Python
) else (
    echo ❌ فشل في إنشاء الاختصار
)

echo.
pause
"""
    
    with open(os.path.join(standalone_dir, "إنشاء_اختصار_سطح_المكتب.bat"), "w", encoding="utf-8") as f:
        f.write(shortcut_script)
    
    print("   ✅ تم إنشاء سكريپت اختصار سطح المكتب")

def create_standalone_zip(standalone_dir):
    """Create ZIP package for standalone version"""
    
    zip_filename = f"{standalone_dir}.zip"
    
    print(f"   📦 إنشاء الملف المضغوط: {zip_filename}")
    
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(standalone_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, standalone_dir)
                zipf.write(file_path, arc_name)
    
    # Get ZIP size
    zip_size = os.path.getsize(zip_filename) / (1024 * 1024)
    print(f"   📊 حجم الملف المضغوط: {zip_size:.1f} MB")

def main():
    """Main function to create standalone version"""
    
    print("🚀 إنشاء النسخة المستقلة لنظام إدارة الرواتب القطري...")
    print("   (تعمل بدون Python أو أي متطلبات أخرى)")
    print()
    
    # Check if PyInstaller is available
    if not install_pyinstaller():
        print("❌ لا يمكن المتابعة بدون PyInstaller")
        return False
    
    # Create spec file
    create_spec_file()
    
    # Build executable
    if not build_executable():
        print("❌ فشل في بناء الملف التنفيذي")
        return False
    
    # Create package
    if not create_standalone_package():
        print("❌ فشل في إنشاء الحزمة")
        return False
    
    print()
    print("🎉 تم إنشاء النسخة المستقلة بنجاح!")
    print()
    print("📁 المجلد: Qatar_Salary_System_Standalone")
    print("📦 الملف المضغوط: Qatar_Salary_System_Standalone.zip")
    print()
    print("✅ النسخة المستقلة تعمل بدون:")
    print("   - Python")
    print("   - مكتبات إضافية")
    print("   - اتصال إنترنت")
    print()
    print("🚀 للتشغيل: اضغط مرتين على 🚀_تشغيل_مباشر.bat")
    
    return True

if __name__ == "__main__":
    main()

@echo off
chcp 65001 >nul
title إنشاء اختصار سطح المكتب

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║              إنشاء اختصار على سطح المكتب                  ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

set "current_dir=%~dp0"
set "desktop=%USERPROFILE%\Desktop"
set "shortcut_name=🇶🇦 نظام إدارة الرواتب القطري.lnk"

echo 🔧 جاري إنشاء الاختصار...

REM Create VBS script to create shortcut
echo Set oWS = WScript.CreateObject("WScript.Shell") > temp_shortcut.vbs
echo sLinkFile = "%desktop%\%shortcut_name%" >> temp_shortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> temp_shortcut.vbs
echo oLink.TargetPath = "%current_dir%🚀_تشغيل_مباشر.bat" >> temp_shortcut.vbs
echo oLink.WorkingDirectory = "%current_dir%" >> temp_shortcut.vbs
echo oLink.IconLocation = "%current_dir%qatar_salary_icon.ico" >> temp_shortcut.vbs
echo oLink.Description = "نظام إدارة الرواتب القطري - تشغيل مباشر" >> temp_shortcut.vbs
echo oLink.Save >> temp_shortcut.vbs

REM Execute VBS script
cscript //nologo temp_shortcut.vbs

REM Clean up
del temp_shortcut.vbs

if exist "%desktop%\%shortcut_name%" (
    echo ✅ تم إنشاء الاختصار بنجاح على سطح المكتب!
    echo 🎯 يمكنك الآن تشغيل النظام من سطح المكتب
) else (
    echo ❌ فشل في إنشاء الاختصار
)

echo.
pause

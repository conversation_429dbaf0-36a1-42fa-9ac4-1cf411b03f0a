#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to update the database schema to add gender column to workers table
"""

import sqlite3
import os

def update_database():
    """Add gender column to workers table"""
    db_path = 'qatar_salary_system.db'
    
    if not os.path.exists(db_path):
        print("Database file not found. Please run the main application first to create the database.")
        return
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if gender column already exists
        cursor.execute("PRAGMA table_info(worker)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'gender' in columns:
            print("Gender column already exists in workers table.")
            conn.close()
            return
        
        print("Adding gender column to workers table...")
        
        # Add gender column
        cursor.execute("ALTER TABLE worker ADD COLUMN gender VARCHAR(10)")
        
        # Update existing workers with default gender (you may want to update this manually)
        cursor.execute("UPDATE worker SET gender = 'ذكر' WHERE gender IS NULL")
        
        # Commit changes
        conn.commit()
        
        print("Successfully added gender column to workers table.")
        print("All existing workers have been set to 'ذكر' by default.")
        print("You can update individual workers through the web interface.")
        
    except sqlite3.Error as e:
        print(f"Database error: {e}")
    except Exception as e:
        print(f"Error: {e}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    update_database()

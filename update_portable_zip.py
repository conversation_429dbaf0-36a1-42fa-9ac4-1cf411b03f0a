#!/usr/bin/env python3
"""
Update the portable ZIP file with new files
"""

import zipfile
import os

def update_portable_zip():
    """Update the ZIP file with latest changes"""
    
    portable_dir = "Qatar_Salary_System_Portable"
    zip_filename = f"{portable_dir}.zip"
    
    print("📦 تحديث الملف المضغوط...")
    
    # Remove old ZIP if exists
    if os.path.exists(zip_filename):
        os.remove(zip_filename)
        print(f"   🗑️ تم حذف الملف القديم: {zip_filename}")
    
    # Create new ZIP
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(portable_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, portable_dir)
                zipf.write(file_path, arc_name)
                print(f"   ✅ تم إضافة: {arc_name}")
    
    # Get file size
    size_mb = os.path.getsize(zip_filename) / (1024 * 1024)
    
    print(f"\n✅ تم إنشاء الملف المضغوط: {zip_filename}")
    print(f"📊 حجم الملف: {size_mb:.2f} MB")
    print("\n🎉 النسخة المحمولة جاهزة للتوزيع!")

if __name__ == "__main__":
    update_portable_zip()

# نظام إدارة الرواتب القطري - النسخة الآمنة
# Qatar Salary Management System - Secure Version

## 🔒 النسخة الآمنة - بدون مستخدم افتراضي

### ✅ مميزات الأمان:
- لا يوجد مستخدم افتراضي (admin/admin123)
- يجب إنشاء حساب جديد لكل مستخدم
- أول مستخدم يصبح مدير تلقائياً
- كلمات مرور قوية مطلوبة
- تشفير كامل لكلمات المرور

### 🚀 أول استخدام:
1. شغل النظام
2. ستظهر صفحة إنشاء حساب جديد
3. أدخل بياناتك (اسم المستخدم، البريد، كلمة المرور)
4. ستحصل على صلاحيات المدير تلقائياً
5. يمكنك إضافة مستخدمين آخرين لاحقاً

### 🖱️ طرق التشغيل:

#### Windows:
1. **التشغيل الآمن:** 🚀_تشغيل_آمن.bat
2. **التشغيل السريع:** تشغيل_سريع.bat
3. **إنشاء اختصار:** إنشاء_اختصار_سطح_المكتب.bat

### 🌐 الوصول للنظام:
العنوان: http://localhost:7474

### 🔐 إنشاء الحساب الأول:
- لا يوجد مستخدم افتراضي
- يجب إنشاء حساب جديد
- أول مستخدم = مدير النظام
- كلمة مرور قوية مطلوبة (أكثر من 6 أحرف)

### 📋 المتطلبات:
- Python 3.7+ مثبت على النظام
- اتصال إنترنت (أول مرة فقط)
- 100 MB مساحة فارغة
- 512 MB ذاكرة عشوائية

### ✨ الميزات الكاملة:
✅ إدارة العمال مع تحديد الجنس (ذكر/أنثى)
✅ حساب الرواتب تلقائياً مع المكافآت والخصومات
✅ تقارير مفصلة وإحصائيات متقدمة
✅ شريط متحرك للرواتب مع أيقونات الجنس
✅ واجهة عربية جميلة ومتجاوبة
✅ نظام مستخدمين آمن ومحسن
✅ تشفير كامل للبيانات الحساسة

### 🛠️ استكشاف الأخطاء:
1. تأكد من تثبيت Python
2. تأكد من الاتصال بالإنترنت
3. شغل كمدير (Run as Administrator)
4. استخدم التشغيل الآمن للحصول على رسائل مفصلة

### 🎯 الجديد في النسخة الآمنة:
✅ إزالة المستخدم الافتراضي نهائياً
✅ نظام تسجيل مستخدمين محسن
✅ أمان عالي مع كلمات مرور قوية
✅ أول مستخدم يصبح مدير تلقائياً
✅ واجهة تسجيل جميلة ومتطورة
✅ حماية شاملة للبيانات

---
🇶🇦 صنع بحب في قطر - النسخة الآمنة بدون مستخدم افتراضي

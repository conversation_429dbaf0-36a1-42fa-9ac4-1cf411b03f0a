<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة رواتب العمالة المنزلية - دولة قطر{% endblock %}</title>
    
    <!-- Bootstrap CSS with RTL support -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        .navbar {
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border-radius: 0.5rem;
        }
        .btn {
            border-radius: 0.375rem;
        }
        .table th {
            background-color: #495057;
            color: white;
            border: none;
        }
        .badge {
            font-size: 0.75em;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 1rem;
        }
        .stats-card h3 {
            font-size: 2rem;
            font-weight: bold;
        }

        /* Salary Ticker Styles */
        .salary-ticker-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8px 0;
            overflow: hidden;
            position: relative;
            border-bottom: 2px solid #007bff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .ticker-controls {
            position: absolute;
            top: 50%;
            left: 10px;
            transform: translateY(-50%);
            z-index: 10;
            display: flex;
            gap: 5px;
        }

        .ticker-controls .btn {
            padding: 0.2rem 0.4rem;
            font-size: 0.75rem;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .salary-ticker {
            white-space: nowrap;
            overflow: hidden;
        }

        .ticker-content {
            display: inline-block;
            animation: scroll-right 60s linear infinite;
            padding-left: 100%;
        }

        .ticker-item {
            display: inline-block;
            margin-left: 3rem;
            padding: 0.25rem 0.75rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .worker-gender {
            margin-left: 0.3rem;
        }

        .worker-name {
            font-weight: bold;
            margin-left: 0.5rem;
            color: #fff;
        }

        .salary-amount {
            font-weight: 600;
            color: #ffd700;
            margin-left: 0.5rem;
        }

        .salary-month {
            font-size: 0.85em;
            color: #e0e0e0;
            margin-left: 0.5rem;
        }

        .ticker-item .badge {
            font-size: 0.7em;
            margin-left: 0.5rem;
        }

        @keyframes scroll-right {
            0% {
                transform: translate3d(-100%, 0, 0);
            }
            100% {
                transform: translate3d(100%, 0, 0);
            }
        }

        /* Pause animation on hover */
        .salary-ticker-container:hover .ticker-content {
            animation-play-state: paused;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .ticker-item {
                margin-left: 2rem;
                padding: 0.2rem 0.5rem;
                font-size: 0.85em;
            }

            .ticker-content {
                animation-duration: 45s;
            }
        }

        @media (max-width: 576px) {
            .ticker-item {
                margin-left: 1.5rem;
                padding: 0.15rem 0.4rem;
                font-size: 0.8em;
            }

            .worker-name, .salary-amount, .salary-month {
                margin-left: 0.3rem;
            }
        }
    </style>
</head>
<body>
    {% if current_user %}
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('dashboard') }}">
                <i class="fas fa-users"></i> نظام الرواتب - قطر 🇶🇦
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('dashboard') }}">
                            <i class="fas fa-tachometer-alt"></i> لوحة التحكم
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('workers') }}">
                            <i class="fas fa-users"></i> العمال
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('salary_payments') }}">
                            <i class="fas fa-money-bill-wave"></i> الرواتب
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('reports') }}">
                            <i class="fas fa-chart-bar"></i> التقارير
                        </a>
                    </li>
                    {% if current_user.is_admin() %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('users') }}">
                            <i class="fas fa-user-cog"></i> إدارة المستخدمين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('settings') }}">
                            <i class="fas fa-cogs"></i> الإعدادات
                        </a>
                    </li>
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user"></i> {{ current_user.username }}
                            <span class="badge bg-{{ 'danger' if current_user.role == 'admin' else 'primary' }} ms-1">
                                {{ 'مدير' if current_user.role == 'admin' else 'مستخدم' }}
                            </span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('profile') }}">
                                <i class="fas fa-user-circle"></i> الملف الشخصي
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                                <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}

    <!-- Salary Ticker -->
    {% if current_user and ticker_data %}
    <div class="salary-ticker-container" id="salaryTicker">
        <div class="ticker-controls">
            <button class="btn btn-sm btn-outline-light" onclick="toggleTicker()" id="tickerToggle">
                <i class="fas fa-pause" id="tickerIcon"></i>
            </button>
            <button class="btn btn-sm btn-outline-light" onclick="hideTicker()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="salary-ticker">
            <div class="ticker-content" id="tickerContent">
                {% for item in ticker_data %}
                <div class="ticker-item">
                    <span class="worker-gender">
                        {% if item.worker_gender == 'ذكر' %}
                            <i class="fas fa-mars text-info"></i>
                        {% else %}
                            <i class="fas fa-venus text-warning"></i>
                        {% endif %}
                    </span>
                    <span class="worker-name">{{ item.worker_name }}</span>
                    <span class="salary-amount">{{ "{:,.0f}".format(item.salary) }} ر.ق</span>
                    <span class="salary-month">{{ item.month }}</span>
                    <span class="badge bg-{{ item.status_color }}">{{ item.status }}</span>
                </div>
                {% endfor %}
                <!-- Repeat items for continuous scroll -->
                {% for item in ticker_data %}
                <div class="ticker-item">
                    <span class="worker-gender">
                        {% if item.worker_gender == 'ذكر' %}
                            <i class="fas fa-mars text-info"></i>
                        {% else %}
                            <i class="fas fa-venus text-warning"></i>
                        {% endif %}
                    </span>
                    <span class="worker-name">{{ item.worker_name }}</span>
                    <span class="salary-amount">{{ "{:,.0f}".format(item.salary) }} ر.ق</span>
                    <span class="salary-month">{{ item.month }}</span>
                    <span class="badge bg-{{ item.status_color }}">{{ item.status }}</span>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Main Content -->
    <main class="{% if current_user %}container-fluid mt-4{% else %}container mt-5{% endif %}">
        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else 'success' if category == 'success' else 'info' }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    {% if current_user %}
    <footer class="bg-light text-center text-muted py-3 mt-5">
        <div class="container">
            <small>&copy; 2024 نظام إدارة رواتب العمالة المنزلية - دولة قطر 🇶🇦. جميع الحقوق محفوظة.</small>
        </div>
    </footer>
    {% endif %}

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Salary Ticker Controls
        let tickerPaused = false;

        function toggleTicker() {
            const tickerContent = document.getElementById('tickerContent');
            const tickerIcon = document.getElementById('tickerIcon');

            if (tickerPaused) {
                tickerContent.style.animationPlayState = 'running';
                tickerIcon.className = 'fas fa-pause';
                tickerPaused = false;
            } else {
                tickerContent.style.animationPlayState = 'paused';
                tickerIcon.className = 'fas fa-play';
                tickerPaused = true;
            }
        }

        function hideTicker() {
            const ticker = document.getElementById('salaryTicker');
            if (ticker) {
                ticker.style.display = 'none';
                localStorage.setItem('tickerHidden', 'true');
            }
        }

        function showTicker() {
            const ticker = document.getElementById('salaryTicker');
            if (ticker) {
                ticker.style.display = 'block';
                localStorage.removeItem('tickerHidden');
            }
        }

        // Check if ticker should be hidden on page load
        document.addEventListener('DOMContentLoaded', function() {
            const tickerHidden = localStorage.getItem('tickerHidden');
            if (tickerHidden === 'true') {
                const ticker = document.getElementById('salaryTicker');
                if (ticker) {
                    ticker.style.display = 'none';
                }
            }
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>

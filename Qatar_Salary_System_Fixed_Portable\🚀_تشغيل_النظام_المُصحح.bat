@echo off
chcp 65001 >nul 2>&1
title 🇶🇦 نظام إدارة الرواتب القطري - النسخة المُصححة
color 0B

cls
echo.
echo        ╔═══════════════════════════════════════════════════════════╗
echo        ║  🇶🇦        نظام إدارة الرواتب القطري        🇶🇦  ║
echo        ║           Qatar Salary Management System              ║
echo        ║                   النسخة المُصححة                    ║
echo        ╚═══════════════════════════════════════════════════════════╝
echo.
echo                    🚀 تشغيل مباشر - بدون أخطاء 🚀
echo.
echo        ✅ تم إصلاح جميع الأخطاء
echo        ✅ حقل الجنس يعمل بشكل مثالي
echo        ✅ أيقونات قطرية جميلة
echo        ✅ واجهة عربية متطورة
echo        ✅ معالجة شاملة للأخطاء
echo.

REM Check if Python is installed
echo 🔍 فحص Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير موجود
    echo.
    echo 📥 يرجى تثبيت Python من: https://python.org
    echo 💡 أو استخدم النسخة المباشرة الذكية
    echo.
    echo 🔗 تحميل Python:
    echo    https://www.python.org/downloads/
    echo.
    pause
    start https://python.org
    exit /b 1
)

echo ✅ Python موجود

REM Check and install requirements
echo 📦 فحص المكتبات...
python -c "import flask, flask_sqlalchemy, werkzeug" >nul 2>&1
if errorlevel 1 (
    echo 🔄 تثبيت المكتبات المطلوبة...
    echo    هذا قد يستغرق دقيقة أو دقيقتين...
    echo.

    python -m pip install --upgrade pip >nul 2>&1
    if errorlevel 1 (
        echo ⚠️ تحديث pip فشل - المتابعة...
    )

    echo    📦 تثبيت Flask...
    python -m pip install flask >nul 2>&1
    if errorlevel 1 (
        echo ❌ فشل في تثبيت Flask
        echo 🌐 تأكد من الاتصال بالإنترنت
        pause
        exit /b 1
    )

    echo    📦 تثبيت Flask-SQLAlchemy...
    python -m pip install flask-sqlalchemy >nul 2>&1
    if errorlevel 1 (
        echo ❌ فشل في تثبيت Flask-SQLAlchemy
        echo 🌐 تأكد من الاتصال بالإنترنت
        pause
        exit /b 1
    )

    echo    📦 تثبيت Werkzeug...
    python -m pip install werkzeug >nul 2>&1
    if errorlevel 1 (
        echo ❌ فشل في تثبيت Werkzeug
        echo 🌐 تأكد من الاتصال بالإنترنت
        pause
        exit /b 1
    )

    echo ✅ تم تثبيت جميع المكتبات بنجاح
) else (
    echo ✅ جميع المكتبات موجودة
)

echo.
echo ┌──────────────────────────────────────────────────────────────┐
echo │  🚀 جاري تشغيل النظام...                                     │
echo │                                                              │
echo │  🌐 العنوان: http://localhost:7474                          │
echo │  👤 المستخدم: admin                                         │
echo │  🔑 كلمة المرور: admin123                                   │
echo │                                                              │
echo │  ✨ الميزات الجديدة:                                        │
echo │     • إدارة العمال مع تحديد الجنس (بدون أخطاء)             │
echo │     • شريط متحرك للرواتب مع الأيقونات                      │
echo │     • أيقونات قطرية جميلة                                  │
echo │     • تقارير مفصلة ومتطورة                                 │
echo │     • معالجة شاملة للأخطاء                                  │
echo │                                                              │
echo │  🛑 للإيقاف اضغط Ctrl+C                                     │
echo └──────────────────────────────────────────────────────────────┘
echo.

REM Test the application first
echo 🧪 اختبار التطبيق...
python -c "import simple_app; print('✅ التطبيق جاهز للتشغيل')" 2>nul
if errorlevel 1 (
    echo ❌ خطأ في التطبيق
    echo 📝 تفاصيل الخطأ:
    python -c "import simple_app"
    echo.
    pause
    exit /b 1
)

REM Open browser after 3 seconds
start /min cmd /c "timeout /t 3 /nobreak >nul && start http://localhost:7474"

REM Start the application
python simple_app.py

echo.
echo ┌──────────────────────────────────────────────────────────────┐
echo │  ✅ تم إيقاف النظام بنجاح                                    │
echo │  🙏 شكراً لاستخدام نظام إدارة الرواتب القطري               │
echo │  🇶🇦 النسخة المُصححة - بدون أخطاء                          │
echo └──────────────────────────────────────────────────────────────┘
echo.
pause

{% extends "base.html" %}

{% block title %}Salary Payments - Salary System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-money-bill-wave"></i> Salary Payments</h1>
            <a href="{{ url_for('add_salary_payment') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add Payment
            </a>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-3">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Status</label>
                        <select name="status" class="form-select">
                            <option value="">All Status</option>
                            <option value="paid" {{ 'selected' if current_status == 'paid' }}>Paid</option>
                            <option value="unpaid" {{ 'selected' if current_status == 'unpaid' }}>Unpaid</option>
                            <option value="partial" {{ 'selected' if current_status == 'partial' }}>Partial</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Worker</label>
                        <select name="worker_id" class="form-select">
                            <option value="">All Workers</option>
                            {% for worker in workers %}
                            <option value="{{ worker.id }}" {{ 'selected' if current_worker_id == worker.id }}>
                                {{ worker.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-outline-primary me-2">
                            <i class="fas fa-filter"></i> Filter
                        </button>
                        <a href="{{ url_for('salary_payments') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> Clear
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Payments Table -->
<div class="card">
    <div class="card-body">
        {% if payments.items %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>Worker</th>
                        <th>Month</th>
                        <th>Base Salary</th>
                        <th>Overtime</th>
                        <th>Bonuses</th>
                        <th>Deductions</th>
                        <th>Total Amount</th>
                        <th>Payment Date</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for payment in payments.items %}
                    <tr>
                        <td>
                            <strong>{{ payment.worker.name }}</strong><br>
                            <small class="text-muted">{{ payment.worker.job_type }}</small>
                        </td>
                        <td>{{ payment.month }}</td>
                        <td>{{ "%.2f"|format(payment.base_salary) }} SAR</td>
                        <td>
                            {% if payment.overtime_hours > 0 %}
                                {{ payment.overtime_hours }}h<br>
                                <small class="text-muted">{{ "%.2f"|format(payment.overtime_hours * payment.overtime_rate) }} SAR</small>
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            {% if payment.bonuses > 0 %}
                                {{ "%.2f"|format(payment.bonuses) }} SAR
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            {% if payment.deductions > 0 %}
                                {{ "%.2f"|format(payment.deductions) }} SAR
                            {% else %}
                                -
                            {% endif %}
                        </td>
                        <td>
                            <strong>{{ "%.2f"|format(payment.total_amount) }} SAR</strong>
                        </td>
                        <td>
                            {% if payment.payment_date %}
                                {{ payment.payment_date.strftime('%Y-%m-%d') }}<br>
                                <small class="text-muted">{{ payment.payment_method or 'N/A' }}</small>
                            {% else %}
                                <span class="text-muted">Not set</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-{{ 'success' if payment.status == 'paid' else 'warning' if payment.status == 'partial' else 'danger' }}">
                                {{ payment.status.title() }}
                            </span>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('view_salary_payment', id=payment.id) }}" 
                                   class="btn btn-outline-info" title="View">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('edit_salary_payment', id=payment.id) }}" 
                                   class="btn btn-outline-primary" title="Edit">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('salary_receipt', id=payment.id) }}" 
                                   class="btn btn-outline-success" title="Download Receipt">
                                    <i class="fas fa-download"></i>
                                </a>
                                {% if current_user.is_admin() %}
                                <button type="button" class="btn btn-outline-danger" 
                                        onclick="confirmDelete({{ payment.id }}, '{{ payment.worker.name }} - {{ payment.month }}')" 
                                        title="Delete">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if payments.pages > 1 %}
        <nav aria-label="Payments pagination">
            <ul class="pagination justify-content-center">
                {% if payments.has_prev %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('salary_payments', page=payments.prev_num, status=current_status, worker_id=current_worker_id) }}">Previous</a>
                    </li>
                {% endif %}
                
                {% for page_num in payments.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != payments.page %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('salary_payments', page=page_num, status=current_status, worker_id=current_worker_id) }}">{{ page_num }}</a>
                            </li>
                        {% else %}
                            <li class="page-item active">
                                <span class="page-link">{{ page_num }}</span>
                            </li>
                        {% endif %}
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    {% endif %}
                {% endfor %}
                
                {% if payments.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('salary_payments', page=payments.next_num, status=current_status, worker_id=current_worker_id) }}">Next</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">No salary payments found</h4>
            <p class="text-muted">Start by adding salary payments for your workers.</p>
            <a href="{{ url_for('add_salary_payment') }}" class="btn btn-primary">
                <i class="fas fa-plus"></i> Add Payment
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete payment for <strong id="paymentInfo"></strong>?</p>
                <p class="text-danger"><small>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(paymentId, paymentInfo) {
    document.getElementById('paymentInfo').textContent = paymentInfo;
    document.getElementById('deleteForm').action = '/salary-payments/' + paymentId + '/delete';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}

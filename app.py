from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify, send_file
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user
from werkzeug.utils import secure_filename
from werkzeug.security import generate_password_hash
import os
from datetime import datetime, date
import pandas as pd
from io import BytesIO
import json

from config import Config
from models import db, User, Worker, SalaryPayment, SystemSettings
from forms import LoginForm, WorkerForm, SalaryPaymentForm, UserForm, SettingsForm, ReportForm
from utils import generate_salary_receipt, allowed_file, create_default_settings, init_admin_user

app = Flask(__name__)
app.config.from_object(Config)

# Initialize extensions
db.init_app(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'Please log in to access this page.'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Create upload directories
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'photos'), exist_ok=True)
os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'documents'), exist_ok=True)

@app.before_request
def create_tables():
    if not hasattr(app, 'tables_created'):
        db.create_all()
        create_default_settings()
        init_admin_user()
        app.tables_created = True

@app.route('/')
@login_required
def dashboard():
    # Dashboard statistics
    total_workers = Worker.query.filter_by(is_active=True).count()
    total_payments_this_month = SalaryPayment.query.filter(
        SalaryPayment.month == datetime.now().strftime('%Y-%m')
    ).count()
    unpaid_payments = SalaryPayment.query.filter_by(status='unpaid').count()
    total_amount_this_month = db.session.query(db.func.sum(SalaryPayment.total_amount)).filter(
        SalaryPayment.month == datetime.now().strftime('%Y-%m'),
        SalaryPayment.status == 'paid'
    ).scalar() or 0
    
    # Recent payments
    recent_payments = SalaryPayment.query.order_by(SalaryPayment.created_at.desc()).limit(5).all()
    
    # Workers with unpaid salaries
    unpaid_workers = db.session.query(Worker).join(SalaryPayment).filter(
        SalaryPayment.status == 'unpaid'
    ).distinct().limit(5).all()
    
    return render_template('dashboard.html',
                         total_workers=total_workers,
                         total_payments_this_month=total_payments_this_month,
                         unpaid_payments=unpaid_payments,
                         total_amount_this_month=total_amount_this_month,
                         recent_payments=recent_payments,
                         unpaid_workers=unpaid_workers)

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    
    form = LoginForm()
    if form.validate_on_submit():
        user = User.query.filter_by(username=form.username.data).first()
        if user and user.check_password(form.password.data) and user.is_active:
            login_user(user)
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('dashboard'))
        flash('Invalid username or password', 'error')
    
    return render_template('login.html', form=form)

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('login'))

@app.route('/workers')
@login_required
def workers():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)
    
    query = Worker.query
    if search:
        query = query.filter(Worker.name.contains(search) | 
                           Worker.nationality.contains(search) |
                           Worker.id_number.contains(search))
    
    workers = query.order_by(Worker.name).paginate(
        page=page, per_page=app.config['WORKERS_PER_PAGE'], error_out=False)
    
    return render_template('workers/list.html', workers=workers, search=search)

@app.route('/workers/add', methods=['GET', 'POST'])
@login_required
def add_worker():
    form = WorkerForm()
    if form.validate_on_submit():
        # Check if ID number already exists
        existing_worker = Worker.query.filter_by(id_number=form.id_number.data).first()
        if existing_worker:
            flash('A worker with this ID number already exists', 'error')
            return render_template('workers/form.html', form=form, title='Add Worker')
        
        worker = Worker(
            name=form.name.data,
            nationality=form.nationality.data,
            id_number=form.id_number.data,
            job_type=form.job_type.data,
            employment_date=form.employment_date.data,
            monthly_salary=form.monthly_salary.data,
            is_active=form.is_active.data
        )
        
        # Handle photo upload
        if form.photo.data:
            photo = form.photo.data
            if allowed_file(photo.filename):
                filename = secure_filename(f"{worker.id_number}_{photo.filename}")
                photo_path = os.path.join(app.config['UPLOAD_FOLDER'], 'photos', filename)
                photo.save(photo_path)
                worker.photo_filename = filename
        
        # Handle documents upload
        if form.documents.data:
            documents = form.documents.data
            if allowed_file(documents.filename):
                filename = secure_filename(f"{worker.id_number}_docs_{documents.filename}")
                docs_path = os.path.join(app.config['UPLOAD_FOLDER'], 'documents', filename)
                documents.save(docs_path)
                worker.documents_path = filename
        
        db.session.add(worker)
        db.session.commit()
        flash('Worker added successfully', 'success')
        return redirect(url_for('workers'))
    
    return render_template('workers/form.html', form=form, title='Add Worker')

@app.route('/workers/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_worker(id):
    worker = Worker.query.get_or_404(id)
    form = WorkerForm(obj=worker)

    if form.validate_on_submit():
        # Check if ID number already exists (excluding current worker)
        existing_worker = Worker.query.filter(
            Worker.id_number == form.id_number.data,
            Worker.id != id
        ).first()
        if existing_worker:
            flash('A worker with this ID number already exists', 'error')
            return render_template('workers/form.html', form=form, title='Edit Worker')

        # Update worker data
        worker.name = form.name.data
        worker.nationality = form.nationality.data
        worker.id_number = form.id_number.data
        worker.job_type = form.job_type.data
        worker.employment_date = form.employment_date.data
        worker.monthly_salary = form.monthly_salary.data
        worker.is_active = form.is_active.data

        # Handle photo upload
        if form.photo.data:
            photo = form.photo.data
            if allowed_file(photo.filename):
                filename = secure_filename(f"{worker.id_number}_{photo.filename}")
                photo_path = os.path.join(app.config['UPLOAD_FOLDER'], 'photos', filename)
                photo.save(photo_path)
                worker.photo_filename = filename

        # Handle documents upload
        if form.documents.data:
            documents = form.documents.data
            if allowed_file(documents.filename):
                filename = secure_filename(f"{worker.id_number}_docs_{documents.filename}")
                docs_path = os.path.join(app.config['UPLOAD_FOLDER'], 'documents', filename)
                documents.save(docs_path)
                worker.documents_path = filename

        db.session.commit()
        flash('Worker updated successfully', 'success')
        return redirect(url_for('workers'))

    return render_template('workers/form.html', form=form, title='Edit Worker')

@app.route('/workers/<int:id>/view')
@login_required
def view_worker(id):
    worker = Worker.query.get_or_404(id)
    recent_payments = SalaryPayment.query.filter_by(worker_id=id).order_by(
        SalaryPayment.created_at.desc()).limit(5).all()
    return render_template('workers/view.html', worker=worker, recent_payments=recent_payments)

@app.route('/workers/<int:id>/delete', methods=['POST'])
@login_required
def delete_worker(id):
    if not current_user.is_admin():
        flash('Access denied. Admin privileges required.', 'error')
        return redirect(url_for('workers'))

    worker = Worker.query.get_or_404(id)

    # Check if worker has salary payments
    if worker.salary_payments:
        flash('Cannot delete worker with existing salary payments', 'error')
        return redirect(url_for('workers'))

    db.session.delete(worker)
    db.session.commit()
    flash('Worker deleted successfully', 'success')
    return redirect(url_for('workers'))

@app.route('/salary-payments')
@login_required
def salary_payments():
    page = request.args.get('page', 1, type=int)
    status = request.args.get('status', '', type=str)
    worker_id = request.args.get('worker_id', 0, type=int)

    query = SalaryPayment.query
    if status:
        query = query.filter_by(status=status)
    if worker_id:
        query = query.filter_by(worker_id=worker_id)

    payments = query.order_by(SalaryPayment.created_at.desc()).paginate(
        page=page, per_page=app.config['PAYMENTS_PER_PAGE'], error_out=False)

    workers = Worker.query.filter_by(is_active=True).all()

    return render_template('salary_payments/list.html',
                         payments=payments,
                         workers=workers,
                         current_status=status,
                         current_worker_id=worker_id)

@app.route('/salary-payments/add', methods=['GET', 'POST'])
@login_required
def add_salary_payment():
    form = SalaryPaymentForm()
    workers = Worker.query.filter_by(is_active=True).all()
    form.worker_id.choices = [(w.id, w.name) for w in workers]

    # Create worker salaries dictionary for JavaScript
    worker_salaries = {str(w.id): float(w.monthly_salary) for w in workers}

    if form.validate_on_submit():
        payment = SalaryPayment(
            worker_id=form.worker_id.data,
            month=form.month.data,
            base_salary=form.base_salary.data,
            overtime_hours=form.overtime_hours.data,
            overtime_rate=form.overtime_rate.data,
            bonuses=form.bonuses.data,
            deductions=form.deductions.data,
            vacation_days=form.vacation_days.data,
            payment_date=form.payment_date.data,
            payment_method=form.payment_method.data,
            status=form.status.data,
            notes=form.notes.data,
            created_by=current_user.id
        )
        payment.calculate_total()

        db.session.add(payment)
        db.session.commit()
        flash('Salary payment added successfully', 'success')
        return redirect(url_for('salary_payments'))

    return render_template('salary_payments/form.html',
                         form=form,
                         title='Add Salary Payment',
                         worker_salaries=worker_salaries)

@app.route('/uploads/<path:filename>')
@login_required
def uploaded_file(filename):
    return send_file(os.path.join(app.config['UPLOAD_FOLDER'], filename))

@app.route('/salary-payments/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_salary_payment(id):
    payment = SalaryPayment.query.get_or_404(id)
    form = SalaryPaymentForm(obj=payment)
    workers = Worker.query.filter_by(is_active=True).all()
    form.worker_id.choices = [(w.id, w.name) for w in workers]

    # Create worker salaries dictionary for JavaScript
    worker_salaries = {str(w.id): float(w.monthly_salary) for w in workers}

    if form.validate_on_submit():
        payment.worker_id = form.worker_id.data
        payment.month = form.month.data
        payment.base_salary = form.base_salary.data
        payment.overtime_hours = form.overtime_hours.data
        payment.overtime_rate = form.overtime_rate.data
        payment.bonuses = form.bonuses.data
        payment.deductions = form.deductions.data
        payment.vacation_days = form.vacation_days.data
        payment.payment_date = form.payment_date.data
        payment.payment_method = form.payment_method.data
        payment.status = form.status.data
        payment.notes = form.notes.data
        payment.calculate_total()

        db.session.commit()
        flash('Salary payment updated successfully', 'success')
        return redirect(url_for('salary_payments'))

    return render_template('salary_payments/form.html',
                         form=form,
                         title='Edit Salary Payment',
                         worker_salaries=worker_salaries)

@app.route('/salary-payments/<int:id>/view')
@login_required
def view_salary_payment(id):
    payment = SalaryPayment.query.get_or_404(id)
    return render_template('salary_payments/view.html', payment=payment)

@app.route('/salary-payments/<int:id>/delete', methods=['POST'])
@login_required
def delete_salary_payment(id):
    if not current_user.is_admin():
        flash('Access denied. Admin privileges required.', 'error')
        return redirect(url_for('salary_payments'))

    payment = SalaryPayment.query.get_or_404(id)
    db.session.delete(payment)
    db.session.commit()
    flash('Salary payment deleted successfully', 'success')
    return redirect(url_for('salary_payments'))

@app.route('/salary-payments/<int:id>/receipt')
@login_required
def salary_receipt(id):
    payment = SalaryPayment.query.get_or_404(id)
    language = request.args.get('lang', 'en')

    pdf_buffer = generate_salary_receipt(payment, language)

    return send_file(
        pdf_buffer,
        as_attachment=True,
        download_name=f'salary_receipt_{payment.worker.name}_{payment.month}.pdf',
        mimetype='application/pdf'
    )

@app.route('/reports')
@login_required
def reports():
    return render_template('reports/index.html')

@app.route('/users')
@login_required
def users():
    if not current_user.is_admin():
        flash('Access denied. Admin privileges required.', 'error')
        return redirect(url_for('dashboard'))

    users = User.query.all()
    return render_template('admin/users.html', users=users)

@app.route('/settings')
@login_required
def settings():
    if not current_user.is_admin():
        flash('Access denied. Admin privileges required.', 'error')
        return redirect(url_for('dashboard'))

    return render_template('admin/settings.html')

@app.route('/profile')
@login_required
def profile():
    return render_template('profile.html')

if __name__ == '__main__':
    app.run(debug=True)

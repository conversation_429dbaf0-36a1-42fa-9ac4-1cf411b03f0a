@echo off
chcp 65001 >nul 2>&1
title 🇶🇦 نظام إدارة الرواتب القطري - بدون مستخدم افتراضي
color 0B

cls
echo.
echo        ╔═══════════════════════════════════════════════════════════╗
echo        ║  🇶🇦        نظام إدارة الرواتب القطري        🇶🇦  ║
echo        ║           Qatar Salary Management System              ║
echo        ║                بدون مستخدم افتراضي                   ║
echo        ╚═══════════════════════════════════════════════════════════╝
echo.
echo                    🚀 تشغيل آمن - بدون أخطاء 🚀
echo.
echo        ✅ لا يوجد مستخدم افتراضي
echo        ✅ يجب إنشاء حساب جديد
echo        ✅ أول مستخدم يصبح مدير تلقائياً
echo        ✅ أمان عالي ومحسن
echo.

REM Check if Python is installed
echo 🔍 فحص Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير موجود
    echo.
    echo 📥 يرجى تثبيت Python من: https://python.org
    echo.
    pause
    start https://python.org
    exit /b 1
)

echo ✅ Python موجود

REM Check and install requirements
echo 📦 فحص المكتبات...
python -c "import flask, flask_sqlalchemy, werkzeug" >nul 2>&1
if errorlevel 1 (
    echo 🔄 تثبيت المكتبات المطلوبة...
    echo    هذا قد يستغرق دقيقة أو دقيقتين...
    echo.
    
    python -m pip install --upgrade pip >nul 2>&1
    python -m pip install flask flask-sqlalchemy werkzeug >nul 2>&1
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المكتبات
        echo 🌐 تأكد من الاتصال بالإنترنت
        pause
        exit /b 1
    )
    
    echo ✅ تم تثبيت جميع المكتبات بنجاح
) else (
    echo ✅ جميع المكتبات موجودة
)

echo.
echo ┌──────────────────────────────────────────────────────────────┐
echo │  🚀 جاري تشغيل النظام...                                     │
echo │                                                              │
echo │  🌐 العنوان: http://localhost:7474                          │
echo │  👤 لا يوجد مستخدم افتراضي                                  │
echo │  🆕 يجب إنشاء حساب جديد                                     │
echo │  👑 أول مستخدم يصبح مدير                                    │
echo │                                                              │
echo │  ✨ الميزات:                                                │
echo │     • أمان عالي بدون مستخدم افتراضي                        │
echo │     • إدارة العمال مع تحديد الجنس                          │
echo │     • شريط متحرك للرواتب                                   │
echo │     • تقارير مفصلة ومتطورة                                 │
echo │                                                              │
echo │  🛑 للإيقاف اضغط Ctrl+C                                     │
echo └──────────────────────────────────────────────────────────────┘
echo.

REM Test the application first
echo 🧪 اختبار التطبيق...
python -c "import simple_app; print('✅ التطبيق جاهز للتشغيل')" 2>nul
if errorlevel 1 (
    echo ❌ خطأ في التطبيق
    echo 📝 تفاصيل الخطأ:
    python -c "import simple_app"
    echo.
    pause
    exit /b 1
)

REM Open browser after 3 seconds
start /min cmd /c "timeout /t 3 /nobreak >nul && start http://localhost:7474"

REM Start the application
python simple_app.py

echo.
echo ┌──────────────────────────────────────────────────────────────┐
echo │  ✅ تم إيقاف النظام بنجاح                                    │
echo │  🙏 شكراً لاستخدام نظام إدارة الرواتب القطري               │
echo │  🔒 النسخة الآمنة - بدون مستخدم افتراضي                    │
echo └──────────────────────────────────────────────────────────────┘
echo.
pause

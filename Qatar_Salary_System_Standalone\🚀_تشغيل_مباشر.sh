#!/bin/bash

clear
echo ""
echo "    ╔═══════════════════════════════════════════════════════════╗"
echo "    ║  🇶🇦        نظام إدارة الرواتب القطري        🇶🇦  ║"
echo "    ║           Qatar Salary Management System              ║"
echo "    ║                   النسخة المستقلة                    ║"
echo "    ╚═══════════════════════════════════════════════════════════╝"
echo ""
echo "                🚀 تشغيل مباشر - بدون متطلبات 🚀"
echo ""
echo "    ✅ لا تحتاج Python أو أي مكتبات"
echo "    ✅ تعمل مباشرة من الفلاش ميموري"
echo "    ✅ سرعة فائقة في التشغيل"
echo ""
echo "    🌐 العنوان: http://localhost:7474"
echo "    👤 المستخدم: admin"
echo "    🔑 كلمة المرور: admin123"
echo ""
echo "    🔄 جاري التشغيل..."

# Open browser
if command -v xdg-open &> /dev/null; then
    xdg-open http://localhost:7474 &
elif command -v open &> /dev/null; then
    open http://localhost:7474 &
fi

# Run the application
./نظام_إدارة_الرواتب_القطري.exe

echo ""
echo "    ✅ تم إيقاف النظام"

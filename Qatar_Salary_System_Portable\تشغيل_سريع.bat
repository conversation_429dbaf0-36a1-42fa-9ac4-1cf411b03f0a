@echo off
chcp 65001 >nul
title نظام إدارة الرواتب القطري

echo.
echo ========================================
echo    نظام إدارة الرواتب القطري
echo    Qatar Salary Management System
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo.
    echo يرجى تثبيت Python من: https://python.org
    echo ثم تشغيل install.bat أولاً
    echo.
    pause
    exit /b 1
)

REM Check if requirements are installed
python -c "import flask" >nul 2>&1
if errorlevel 1 (
    echo 📦 جاري تثبيت المتطلبات...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المتطلبات
        echo يرجى تشغيل install.bat أولاً
        pause
        exit /b 1
    )
)

echo ✅ جاري تشغيل النظام...
echo.
echo 🌐 سيتم فتح النظام في المتصفح على:
echo    http://localhost:7474
echo.
echo 🔐 بيانات الدخول:
echo    اسم المستخدم: admin
echo    كلمة المرور: admin123
echo.
echo 🛑 للإيقاف اضغط Ctrl+C
echo.

REM Start the application
python simple_app.py

echo.
echo تم إيقاف النظام
pause

# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# Add all template and static files
datas = []

# Add templates directory
import os
for root, dirs, files in os.walk('templates'):
    for file in files:
        file_path = os.path.join(root, file)
        datas.append((file_path, root))

# Add static directory if exists
if os.path.exists('static'):
    for root, dirs, files in os.walk('static'):
        for file in files:
            file_path = os.path.join(root, file)
            datas.append((file_path, root))

a = Analysis(
    ['simple_app.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=[
        'flask',
        'flask_sqlalchemy',
        'werkzeug',
        'werkzeug.security',
        'jinja2',
        'sqlite3',
        'datetime',
        'os',
        'hashlib',
        'functools',
        'sqlalchemy',
        'sqlalchemy.sql.default_comparator',
        'email_validator'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='نظام_إدارة_الرواتب_القطري',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='Qatar_Salary_System_Portable/qatar_salary_icon.ico' if os.path.exists('Qatar_Salary_System_Portable/qatar_salary_icon.ico') else None,
)

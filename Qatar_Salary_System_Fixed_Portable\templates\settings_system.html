{% extends "simple_base.html" %}

{% block title %}إعدادات النظام - نظام الرواتب{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-server"></i> إعدادات النظام</h1>
            <a href="{{ url_for('settings') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> العودة للإعدادات
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- Database Management -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5><i class="fas fa-database"></i> إدارة قاعدة البيانات</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6>النسخ الاحتياطية</h6>
                    <p class="text-muted">إنشاء نسخة احتياطية من قاعدة البيانات للحفاظ على البيانات</p>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="backup_database">
                        <button type="submit" class="btn btn-primary" onclick="return confirm('هل تريد إنشاء نسخة احتياطية من قاعدة البيانات؟')">
                            <i class="fas fa-download"></i> إنشاء نسخة احتياطية
                        </button>
                    </form>
                </div>
                
                <hr>
                
                <div class="mb-3">
                    <h6>تحسين قاعدة البيانات</h6>
                    <p class="text-muted">تحسين أداء قاعدة البيانات وتقليل حجمها</p>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="optimize_database">
                        <button type="submit" class="btn btn-success" onclick="return confirm('هل تريد تحسين قاعدة البيانات؟')">
                            <i class="fas fa-magic"></i> تحسين قاعدة البيانات
                        </button>
                    </form>
                </div>
                
                <hr>
                
                <div class="mb-3">
                    <h6>مسح السجلات</h6>
                    <p class="text-muted">مسح سجلات النظام القديمة لتوفير مساحة</p>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="action" value="clear_logs">
                        <button type="submit" class="btn btn-warning" onclick="return confirm('هل تريد مسح سجلات النظام؟')">
                            <i class="fas fa-trash"></i> مسح السجلات
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- System Information -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5><i class="fas fa-info-circle"></i> معلومات النظام</h5>
            </div>
            <div class="card-body">
                <table class="table table-borderless">
                    <tr>
                        <td><strong>إصدار Python:</strong></td>
                        <td>{{ system_info.python_version }}</td>
                    </tr>
                    <tr>
                        <td><strong>نظام التشغيل:</strong></td>
                        <td>{{ system_info.system }}</td>
                    </tr>
                    <tr>
                        <td><strong>المنصة:</strong></td>
                        <td>{{ system_info.platform }}</td>
                    </tr>
                    <tr>
                        <td><strong>عدد المعالجات:</strong></td>
                        <td>{{ system_info.cpu_count }}</td>
                    </tr>
                    <tr>
                        <td><strong>إجمالي الذاكرة:</strong></td>
                        <td>{{ system_info.memory_total }} GB</td>
                    </tr>
                    <tr>
                        <td><strong>الذاكرة المتاحة:</strong></td>
                        <td>{{ system_info.memory_available }} GB</td>
                    </tr>
                    <tr>
                        <td><strong>استخدام القرص:</strong></td>
                        <td>
                            {{ system_info.disk_usage }}%
                            <div class="progress mt-1" style="height: 8px;">
                                <div class="progress-bar bg-{{ 'danger' if system_info.disk_usage > 80 else 'warning' if system_info.disk_usage > 60 else 'success' }}" 
                                     style="width: {{ system_info.disk_usage }}%"></div>
                            </div>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Database Statistics -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-bar"></i> إحصائيات قاعدة البيانات</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h4>{{ db_stats.total_users }}</h4>
                                <small>المستخدمين</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-user-tie fa-2x mb-2"></i>
                                <h4>{{ db_stats.total_workers }}</h4>
                                <small>العمال</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-money-bill-wave fa-2x mb-2"></i>
                                <h4>{{ db_stats.total_payments }}</h4>
                                <small>الرواتب</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body text-center">
                                <i class="fas fa-database fa-2x mb-2"></i>
                                <h4>{{ "%.2f"|format(db_stats.database_size) }}</h4>
                                <small>حجم قاعدة البيانات (MB)</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Maintenance Schedule -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-calendar-check"></i> جدولة الصيانة</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> توصيات الصيانة:</h6>
                    <ul class="mb-0">
                        <li><strong>النسخ الاحتياطية:</strong> يُنصح بإنشاء نسخة احتياطية أسبوعياً</li>
                        <li><strong>تحسين قاعدة البيانات:</strong> يُنصح بتحسين قاعدة البيانات شهرياً</li>
                        <li><strong>مسح السجلات:</strong> يُنصح بمسح السجلات القديمة كل 3 أشهر</li>
                        <li><strong>مراجعة الأمان:</strong> يُنصح بمراجعة إعدادات الأمان شهرياً</li>
                    </ul>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>آخر النشاطات:</h6>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between">
                                <span>آخر نسخة احتياطية</span>
                                {% if system_info.last_backup %}
                                    <span class="badge bg-success">{{ system_info.last_backup.strftime('%Y-%m-%d') }}</span>
                                {% else %}
                                    <span class="badge bg-warning">لم يتم إنشاء نسخة</span>
                                {% endif %}
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>آخر تحسين لقاعدة البيانات</span>
                                <span class="badge bg-info">غير محدد</span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>آخر مسح للسجلات</span>
                                <span class="badge bg-info">غير محدد</span>
                            </li>
                        </ul>
                    </div>
                    
                    <div class="col-md-6">
                        <h6>حالة النظام:</h6>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>قاعدة البيانات</span>
                            <span class="badge bg-success">متصلة</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>مساحة القرص</span>
                            <span class="badge bg-{{ 'danger' if system_info.disk_usage > 80 else 'warning' if system_info.disk_usage > 60 else 'success' }}">
                                {{ system_info.disk_usage }}% مستخدمة
                            </span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span>الذاكرة</span>
                            <span class="badge bg-success">متاحة</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center">
                            <span>الأداء</span>
                            <span class="badge bg-success">ممتاز</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.btn:hover {
    transform: translateY(-1px);
    transition: all 0.2s;
}

.progress {
    background-color: rgba(255, 255, 255, 0.2);
}

.table td {
    padding: 0.5rem;
    border: none;
}
</style>
{% endblock %}

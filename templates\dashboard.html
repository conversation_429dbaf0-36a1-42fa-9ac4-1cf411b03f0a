{% extends "base.html" %}

{% block title %}Dashboard - Salary System{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4"><i class="fas fa-tachometer-alt"></i> Dashboard</h1>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ total_workers }}</h4>
                        <p class="mb-0">Active Workers</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ total_payments_this_month }}</h4>
                        <p class="mb-0">Payments This Month</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-money-bill-wave fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ unpaid_payments }}</h4>
                        <p class="mb-0">Unpaid Salaries</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ "%.2f"|format(total_amount_this_month) }}</h4>
                        <p class="mb-0">Total Paid (SAR)</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Payments -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-clock"></i> Recent Payments</h5>
            </div>
            <div class="card-body">
                {% if recent_payments %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Worker</th>
                                    <th>Month</th>
                                    <th>Amount</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payment in recent_payments %}
                                <tr>
                                    <td>{{ payment.worker.name }}</td>
                                    <td>{{ payment.month }}</td>
                                    <td>{{ "%.2f"|format(payment.total_amount) }} SAR</td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if payment.status == 'paid' else 'warning' }}">
                                            {{ payment.status.title() }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">No recent payments found.</p>
                {% endif %}
                <div class="text-end">
                    <a href="{{ url_for('salary_payments') }}" class="btn btn-sm btn-primary">View All</a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Workers with Unpaid Salaries -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-exclamation-circle"></i> Unpaid Salaries</h5>
            </div>
            <div class="card-body">
                {% if unpaid_workers %}
                    <div class="list-group list-group-flush">
                        {% for worker in unpaid_workers %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong>{{ worker.name }}</strong><br>
                                <small class="text-muted">{{ worker.job_type }} - {{ worker.nationality }}</small>
                            </div>
                            <span class="badge bg-warning rounded-pill">Unpaid</span>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p class="text-muted">All salaries are up to date!</p>
                {% endif %}
                <div class="text-end mt-3">
                    <a href="{{ url_for('salary_payments', status='unpaid') }}" class="btn btn-sm btn-warning">View All Unpaid</a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-bolt"></i> Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('add_worker') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-user-plus"></i><br>Add Worker
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('add_salary_payment') }}" class="btn btn-outline-success w-100">
                            <i class="fas fa-money-bill-wave"></i><br>Add Payment
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('reports') }}" class="btn btn-outline-info w-100">
                            <i class="fas fa-chart-bar"></i><br>Generate Report
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('workers') }}" class="btn btn-outline-secondary w-100">
                            <i class="fas fa-users"></i><br>View Workers
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

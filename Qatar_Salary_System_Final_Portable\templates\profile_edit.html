{% extends "simple_base.html" %}

{% block title %}تعديل الملف الشخصي - نظام الرواتب{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-user-edit"></i> تعديل الملف الشخصي</h1>
            <a href="{{ url_for('profile') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> العودة للملف الشخصي
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-user"></i> تحديث المعلومات الشخصية</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <h6 class="mb-3"><i class="fas fa-info-circle"></i> المعلومات الأساسية</h6>
                            
                            <div class="mb-3">
                                <label class="form-label">اسم المستخدم</label>
                                <input type="text" name="username" class="form-control" 
                                       value="{{ current_user.username }}" required>
                                <div class="form-text">
                                    <small>يجب أن يكون أكثر من 3 أحرف وفريد</small>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" name="email" class="form-control" 
                                       value="{{ current_user.email }}" required>
                                <div class="form-text">
                                    <small>يجب أن يكون بريد إلكتروني صحيح وفريد</small>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">الدور</label>
                                <input type="text" class="form-control" 
                                       value="{{ 'مدير النظام' if current_user.role == 'admin' else 'مستخدم عادي' }}" 
                                       readonly>
                                <div class="form-text">
                                    <small>لا يمكن تغيير الدور</small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Password Change -->
                        <div class="col-md-6">
                            <h6 class="mb-3"><i class="fas fa-key"></i> تغيير كلمة المرور</h6>
                            
                            <div class="mb-3">
                                <label class="form-label">كلمة المرور الحالية</label>
                                <input type="password" name="current_password" class="form-control">
                                <div class="form-text">
                                    <small>مطلوبة فقط إذا كنت تريد تغيير كلمة المرور</small>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">كلمة المرور الجديدة</label>
                                <input type="password" name="new_password" class="form-control" id="newPassword">
                                <div class="form-text">
                                    <small>يجب أن تكون أكثر من 6 أحرف</small>
                                </div>
                                <div id="password-strength" style="display: none;"></div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">تأكيد كلمة المرور الجديدة</label>
                                <input type="password" name="confirm_password" class="form-control" id="confirmPassword">
                                <div class="form-text">
                                    <small>يجب أن تطابق كلمة المرور الجديدة</small>
                                </div>
                                <div id="password-match" style="display: none;"></div>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <!-- Account Information -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="mb-3"><i class="fas fa-info"></i> معلومات الحساب</h6>
                            <div class="card bg-light">
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <strong>تاريخ الإنشاء:</strong><br>
                                            <span class="text-muted">{{ current_user.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                                        </div>
                                        <div class="col-md-4">
                                            <strong>الحالة:</strong><br>
                                            <span class="badge bg-{{ 'success' if current_user.is_active else 'secondary' }}">
                                                {{ 'نشط' if current_user.is_active else 'غير نشط' }}
                                            </span>
                                        </div>
                                        <div class="col-md-4">
                                            <strong>آخر تحديث:</strong><br>
                                            <span class="text-muted">الآن</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <!-- Form Actions -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="{{ url_for('profile') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> حفظ التغييرات
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Password strength indicator
document.getElementById('newPassword').addEventListener('input', function(e) {
    const password = e.target.value;
    const strength = document.getElementById('password-strength');
    
    if (password.length === 0) {
        strength.style.display = 'none';
        return;
    }
    
    strength.style.display = 'block';
    
    let score = 0;
    if (password.length >= 6) score++;
    if (password.length >= 8) score++;
    if (/[A-Z]/.test(password)) score++;
    if (/[0-9]/.test(password)) score++;
    if (/[^A-Za-z0-9]/.test(password)) score++;
    
    const colors = ['danger', 'warning', 'info', 'success', 'success'];
    const texts = ['ضعيف جداً', 'ضعيف', 'متوسط', 'قوي', 'قوي جداً'];
    
    strength.className = `alert alert-${colors[score]} mt-2`;
    strength.textContent = `قوة كلمة المرور: ${texts[score]}`;
});

// Password match indicator
document.getElementById('confirmPassword').addEventListener('input', function(e) {
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = e.target.value;
    const match = document.getElementById('password-match');
    
    if (confirmPassword.length === 0) {
        match.style.display = 'none';
        return;
    }
    
    match.style.display = 'block';
    
    if (newPassword === confirmPassword) {
        match.className = 'alert alert-success mt-2';
        match.textContent = 'كلمات المرور متطابقة ✓';
    } else {
        match.className = 'alert alert-danger mt-2';
        match.textContent = 'كلمات المرور غير متطابقة ✗';
    }
});

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const currentPassword = document.querySelector('input[name="current_password"]').value;
    
    // If trying to change password
    if (newPassword || confirmPassword || currentPassword) {
        if (!currentPassword) {
            e.preventDefault();
            alert('يجب إدخال كلمة المرور الحالية لتغيير كلمة المرور');
            return;
        }
        
        if (!newPassword) {
            e.preventDefault();
            alert('يجب إدخال كلمة المرور الجديدة');
            return;
        }
        
        if (newPassword !== confirmPassword) {
            e.preventDefault();
            alert('كلمات المرور غير متطابقة');
            return;
        }
        
        if (newPassword.length < 6) {
            e.preventDefault();
            alert('كلمة المرور الجديدة يجب أن تكون أكثر من 6 أحرف');
            return;
        }
    }
});
</script>
{% endblock %}

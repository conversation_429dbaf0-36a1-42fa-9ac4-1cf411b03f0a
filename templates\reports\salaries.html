{% extends "simple_base.html" %}

{% block title %}تقرير الرواتب - نظام الرواتب{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-money-bill-wave"></i> تقرير الرواتب</h1>
            <div>
                <a href="{{ url_for('export_report', report_type='salaries') }}" class="btn btn-success me-2">
                    <i class="fas fa-file-csv"></i> تصدير CSV
                </a>
                <a href="{{ url_for('reports') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> العودة للتقارير
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-filter"></i> تصفية التقرير</h5>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">من تاريخ</label>
                        <input type="month" name="start_date" class="form-control" value="{{ start_date }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">إلى تاريخ</label>
                        <input type="month" name="end_date" class="form-control" value="{{ end_date }}">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">الحالة</label>
                        <select name="status" class="form-select">
                            <option value="">جميع الحالات</option>
                            <option value="paid" {{ 'selected' if current_status == 'paid' }}>مدفوع</option>
                            <option value="unpaid" {{ 'selected' if current_status == 'unpaid' }}>غير مدفوع</option>
                            <option value="partial" {{ 'selected' if current_status == 'partial' }}>جزئي</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">العامل</label>
                        <select name="worker_id" class="form-select">
                            <option value="">جميع العمال</option>
                            {% for worker in workers %}
                            <option value="{{ worker.id }}" {{ 'selected' if current_worker_id == worker.id }}>
                                {{ worker.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> تطبيق التصفية
                        </button>
                        <a href="{{ url_for('salaries_report') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> مسح التصفية
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>{{ total_payments }}</h3>
                        <p class="mb-0">إجمالي المدفوعات</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-list fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>{{ paid_count }}</h3>
                        <p class="mb-0">مدفوع</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>{{ unpaid_count }}</h3>
                        <p class="mb-0">غير مدفوع</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-times fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>{{ "%.0f"|format(total_amount) }}</h3>
                        <p class="mb-0">إجمالي المبلغ (ر.ق)</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-money-bill-wave fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Monthly Statistics -->
{% if monthly_stats %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-line"></i> الإحصائيات الشهرية</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الشهر</th>
                                <th>عدد المدفوعات</th>
                                <th>المدفوعات المكتملة</th>
                                <th>إجمالي المبلغ</th>
                                <th>المبلغ المدفوع</th>
                                <th>نسبة الإكمال</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for month, stats in monthly_stats.items() %}
                            <tr>
                                <td><strong>{{ month }}</strong></td>
                                <td>{{ stats.count }}</td>
                                <td>{{ stats.paid_count }}</td>
                                <td>{{ "%.2f"|format(stats.total_amount) }} ر.ق</td>
                                <td>{{ "%.2f"|format(stats.paid_amount) }} ر.ق</td>
                                <td>
                                    {% set completion = (stats.paid_count / stats.count * 100) if stats.count > 0 else 0 %}
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar bg-{{ 'success' if completion == 100 else 'warning' if completion >= 50 else 'danger' }}" 
                                             role="progressbar" style="width: {{ completion }}%">
                                            {{ "%.0f"|format(completion) }}%
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Payments Details -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-table"></i> تفاصيل المدفوعات</h5>
            </div>
            <div class="card-body">
                {% if payments %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>العامل</th>
                                <th>الشهر</th>
                                <th>الراتب الأساسي</th>
                                <th>الساعات الإضافية</th>
                                <th>المكافآت</th>
                                <th>الخصومات</th>
                                <th>إجمالي المبلغ</th>
                                <th>تاريخ الدفع</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payment in payments %}
                            <tr>
                                <td>
                                    <strong>{{ payment.worker.name }}</strong><br>
                                    <small class="text-muted">{{ payment.worker.nationality }}</small>
                                </td>
                                <td>{{ payment.month }}</td>
                                <td>{{ "%.2f"|format(payment.base_salary) }} ر.ق</td>
                                <td>
                                    {% if payment.overtime_hours > 0 %}
                                        {{ payment.overtime_hours }}h<br>
                                        <small class="text-muted">{{ "%.2f"|format(payment.overtime_hours * payment.overtime_rate) }} ر.ق</small>
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if payment.bonuses > 0 %}
                                        {{ "%.2f"|format(payment.bonuses) }} ر.ق
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if payment.deductions > 0 %}
                                        {{ "%.2f"|format(payment.deductions) }} ر.ق
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td><strong>{{ "%.2f"|format(payment.total_amount) }} ر.ق</strong></td>
                                <td>
                                    {% if payment.payment_date %}
                                        {{ payment.payment_date.strftime('%Y-%m-%d') }}
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if payment.status == 'paid' else 'warning' if payment.status == 'partial' else 'danger' }}">
                                        {% if payment.status == 'paid' %}مدفوع
                                        {% elif payment.status == 'partial' %}جزئي
                                        {% else %}غير مدفوع
                                        {% endif %}
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد مدفوعات</h4>
                    <p class="text-muted">لا توجد مدفوعات تطابق معايير التصفية المحددة.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

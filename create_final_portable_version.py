#!/usr/bin/env python3
"""
Create the final portable version with icons and all bug fixes
"""

import os
import shutil
import zipfile
from pathlib import Path

def create_final_portable_version():
    """Create the final portable version with all fixes"""
    
    print("🚀 إنشاء النسخة المحمولة النهائية مع الأيقونات وبدون أخطاء...")
    
    # Create final portable directory
    final_dir = "Qatar_Salary_System_Final_Portable"
    if os.path.exists(final_dir):
        shutil.rmtree(final_dir)
    os.makedirs(final_dir)
    
    # Copy application files
    copy_application_files(final_dir)
    
    # Copy icons
    copy_icons(final_dir)
    
    # Create enhanced launchers
    create_enhanced_launchers(final_dir)
    
    # Create desktop shortcut scripts
    create_desktop_shortcuts(final_dir)
    
    # Create documentation
    create_final_documentation(final_dir)
    
    # Create autorun files
    create_autorun_files(final_dir)
    
    # Create ZIP package
    create_final_zip(final_dir)
    
    print("🎉 تم إنشاء النسخة المحمولة النهائية بنجاح!")

def copy_application_files(final_dir):
    """Copy all application files with latest fixes"""
    
    print("📁 نسخ ملفات التطبيق المحدثة...")
    
    # Files to copy
    files_to_copy = [
        "simple_app.py",
        "requirements.txt"
    ]
    
    # Directories to copy
    dirs_to_copy = [
        "templates",
        "static"
    ]
    
    # Copy files
    for file in files_to_copy:
        if os.path.exists(file):
            shutil.copy2(file, final_dir)
            print(f"   ✅ تم نسخ: {file}")
    
    # Copy directories
    for dir_name in dirs_to_copy:
        if os.path.exists(dir_name):
            shutil.copytree(dir_name, os.path.join(final_dir, dir_name))
            print(f"   ✅ تم نسخ: {dir_name}")

def copy_icons(final_dir):
    """Copy icon files"""
    
    print("🖼️ نسخ الأيقونات...")
    
    # Icon files to copy
    icon_files = [
        "Qatar_Salary_System_Portable/qatar_salary_icon.ico",
        "Qatar_Salary_System_Portable/qatar_salary_icon.png"
    ]
    
    for icon_file in icon_files:
        if os.path.exists(icon_file):
            dest_file = os.path.join(final_dir, os.path.basename(icon_file))
            shutil.copy2(icon_file, dest_file)
            print(f"   ✅ تم نسخ: {os.path.basename(icon_file)}")
        else:
            print(f"   ⚠️ الأيقونة غير موجودة: {icon_file}")

def create_enhanced_launchers(final_dir):
    """Create enhanced launcher scripts"""
    
    print("🚀 إنشاء ملفات التشغيل المحسنة...")
    
    # Enhanced Windows launcher
    enhanced_launcher = """@echo off
chcp 65001 >nul
title 🇶🇦 نظام إدارة الرواتب القطري - النسخة النهائية
color 0B

cls
echo.
echo        ╔═══════════════════════════════════════════════════════════╗
echo        ║  🇶🇦        نظام إدارة الرواتب القطري        🇶🇦  ║
echo        ║           Qatar Salary Management System              ║
echo        ║                   النسخة النهائية                    ║
echo        ╚═══════════════════════════════════════════════════════════╝
echo.
echo                    🚀 تشغيل مباشر - بدون أخطاء 🚀
echo.
echo        ✅ جميع الأخطاء تم إصلاحها
echo        ✅ حقل الجنس يعمل بشكل مثالي
echo        ✅ أيقونات قطرية جميلة
echo        ✅ واجهة عربية متطورة
echo.

REM Check if Python is installed
echo 🔍 فحص Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير موجود
    echo.
    echo 📥 يرجى تثبيت Python من: https://python.org
    echo 💡 أو استخدم النسخة المباشرة الذكية
    echo.
    pause
    exit /b 1
)

echo ✅ Python موجود

REM Check and install requirements
echo 📦 فحص المكتبات...
python -c "import flask" >nul 2>&1
if errorlevel 1 (
    echo 🔄 تثبيت المكتبات المطلوبة...
    echo    هذا قد يستغرق دقيقة أو دقيقتين...
    python -m pip install --upgrade pip >nul 2>&1
    python -m pip install flask flask-sqlalchemy werkzeug jinja2 >nul 2>&1
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المكتبات
        echo 🌐 تأكد من الاتصال بالإنترنت
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت المكتبات بنجاح
) else (
    echo ✅ المكتبات موجودة
)

echo.
echo ┌──────────────────────────────────────────────────────────────┐
echo │  🚀 جاري تشغيل النظام...                                     │
echo │                                                              │
echo │  🌐 العنوان: http://localhost:7474                          │
echo │  👤 المستخدم: admin                                         │
echo │  🔑 كلمة المرور: admin123                                   │
echo │                                                              │
echo │  ✨ الميزات الجديدة:                                        │
echo │     • إدارة العمال مع تحديد الجنس                          │
echo │     • شريط متحرك للرواتب                                   │
echo │     • أيقونات قطرية جميلة                                  │
echo │     • تقارير مفصلة                                          │
echo │                                                              │
echo │  🛑 للإيقاف اضغط Ctrl+C                                     │
echo └──────────────────────────────────────────────────────────────┘
echo.

REM Open browser after 3 seconds
start /min cmd /c "timeout /t 3 /nobreak >nul && start http://localhost:7474"

REM Start the application
python simple_app.py

echo.
echo ┌──────────────────────────────────────────────────────────────┐
echo │  ✅ تم إيقاف النظام بنجاح                                    │
echo │  🙏 شكراً لاستخدام نظام إدارة الرواتب القطري               │
echo └──────────────────────────────────────────────────────────────┘
echo.
pause
"""
    
    with open(os.path.join(final_dir, "🚀_تشغيل_النظام.bat"), "w", encoding="utf-8") as f:
        f.write(enhanced_launcher)
    
    # Quick launcher
    quick_launcher = """@echo off
chcp 65001 >nul
title نظام الرواتب القطري

echo 🇶🇦 نظام إدارة الرواتب القطري 🇶🇦
echo النسخة النهائية - بدون أخطاء
echo.

python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير موجود - شغل "🚀_تشغيل_النظام.bat"
    pause
    exit
)

python -c "import flask" >nul 2>&1
if errorlevel 1 (
    echo 📦 تثبيت المكتبات...
    pip install flask flask-sqlalchemy >nul
)

echo ✅ جاري التشغيل...
echo 🌐 http://localhost:7474
echo 👤 admin / 🔑 admin123

start http://localhost:7474
python simple_app.py
pause
"""
    
    with open(os.path.join(final_dir, "تشغيل_سريع.bat"), "w", encoding="utf-8") as f:
        f.write(quick_launcher)
    
    # Linux/Mac launcher
    unix_launcher = """#!/bin/bash

clear
echo ""
echo "    ╔═══════════════════════════════════════════════════════════╗"
echo "    ║  🇶🇦        نظام إدارة الرواتب القطري        🇶🇦  ║"
echo "    ║           Qatar Salary Management System              ║"
echo "    ║                   النسخة النهائية                    ║"
echo "    ╚═══════════════════════════════════════════════════════════╝"
echo ""
echo "                🚀 تشغيل مباشر - بدون أخطاء 🚀"
echo ""
echo "    ✅ جميع الأخطاء تم إصلاحها"
echo "    ✅ حقل الجنس يعمل بشكل مثالي"
echo "    ✅ أيقونات قطرية جميلة"
echo "    ✅ واجهة عربية متطورة"
echo ""

# Find Python
if command -v python3 &> /dev/null; then
    PYTHON="python3"
elif command -v python &> /dev/null; then
    PYTHON="python"
else
    echo "    ❌ Python غير موجود - يرجى تثبيته"
    exit 1
fi

echo "    ✅ Python موجود"

# Check and install requirements
$PYTHON -c "import flask" &> /dev/null
if [ $? -ne 0 ]; then
    echo "    📦 تثبيت المكتبات..."
    $PYTHON -m pip install flask flask-sqlalchemy &> /dev/null
    echo "    ✅ تم تثبيت المكتبات"
else
    echo "    ✅ المكتبات موجودة"
fi

echo ""
echo "    🚀 جاري تشغيل النظام..."
echo ""
echo "    🌐 العنوان: http://localhost:7474"
echo "    👤 المستخدم: admin"
echo "    🔑 كلمة المرور: admin123"
echo ""

# Open browser
if command -v xdg-open &> /dev/null; then
    xdg-open http://localhost:7474 &
elif command -v open &> /dev/null; then
    open http://localhost:7474 &
fi

# Start application
$PYTHON simple_app.py

echo ""
echo "    ✅ تم إيقاف النظام"
"""
    
    launcher_path = os.path.join(final_dir, "🚀_تشغيل_النظام.sh")
    with open(launcher_path, "w", encoding="utf-8") as f:
        f.write(unix_launcher)
    
    # Make executable
    try:
        os.chmod(launcher_path, 0o755)
    except:
        pass
    
    print("   ✅ تم إنشاء ملفات التشغيل المحسنة")

def create_desktop_shortcuts(final_dir):
    """Create desktop shortcut scripts"""
    
    print("🔗 إنشاء سكريپتات الاختصارات...")
    
    # Windows desktop shortcut
    shortcut_script = """@echo off
chcp 65001 >nul
title إنشاء اختصار سطح المكتب - النسخة النهائية

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║            إنشاء اختصار على سطح المكتب - النسخة النهائية      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

set "current_dir=%~dp0"
set "desktop=%USERPROFILE%\\Desktop"
set "shortcut_name=🇶🇦 نظام الرواتب القطري (النهائي).lnk"

echo 🔧 جاري إنشاء الاختصار...

REM Create VBS script
echo Set oWS = WScript.CreateObject("WScript.Shell") > temp_shortcut.vbs
echo sLinkFile = "%desktop%\\%shortcut_name%" >> temp_shortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> temp_shortcut.vbs
echo oLink.TargetPath = "%current_dir%🚀_تشغيل_النظام.bat" >> temp_shortcut.vbs
echo oLink.WorkingDirectory = "%current_dir%" >> temp_shortcut.vbs
echo oLink.IconLocation = "%current_dir%qatar_salary_icon.ico" >> temp_shortcut.vbs
echo oLink.Description = "نظام إدارة الرواتب القطري - النسخة النهائية بدون أخطاء" >> temp_shortcut.vbs
echo oLink.Save >> temp_shortcut.vbs

REM Execute VBS script
cscript //nologo temp_shortcut.vbs

REM Clean up
del temp_shortcut.vbs

if exist "%desktop%\\%shortcut_name%" (
    echo ✅ تم إنشاء الاختصار بنجاح على سطح المكتب!
    echo 🎯 يمكنك الآن تشغيل النظام من سطح المكتب
    echo 🚀 النسخة النهائية - بدون أخطاء
    echo 🎨 مع الأيقونات القطرية الجميلة
) else (
    echo ❌ فشل في إنشاء الاختصار
)

echo.
pause
"""
    
    with open(os.path.join(final_dir, "إنشاء_اختصار_سطح_المكتب.bat"), "w", encoding="utf-8") as f:
        f.write(shortcut_script)
    
    # Linux desktop file
    desktop_file = """[Desktop Entry]
Version=1.0
Type=Application
Name=نظام إدارة الرواتب القطري (النهائي)
Name[en]=Qatar Salary Management System (Final)
Comment=نظام شامل لإدارة رواتب العمال في قطر - النسخة النهائية بدون أخطاء
Comment[en]=Comprehensive salary management system for workers in Qatar - Final version without errors
Exec=bash -c "cd '%k' && ./🚀_تشغيل_النظام.sh"
Icon=qatar_salary_icon.png
Terminal=false
Categories=Office;Finance;
StartupNotify=true
"""
    
    with open(os.path.join(final_dir, "Qatar_Salary_System_Final.desktop"), "w", encoding="utf-8") as f:
        f.write(desktop_file)
    
    print("   ✅ تم إنشاء سكريپتات الاختصارات")

def create_final_documentation(final_dir):
    """Create comprehensive documentation"""
    
    print("📖 إنشاء التوثيق الشامل...")
    
    # Main README
    readme_content = """# نظام إدارة الرواتب القطري - النسخة النهائية
# Qatar Salary Management System - Final Version

## 🚀 النسخة النهائية - بدون أخطاء

### ✅ تم إصلاح جميع الأخطاء:
- حقل الجنس يعمل بشكل مثالي
- لا توجد أخطاء في قاعدة البيانات
- معالجة شاملة للاستثناءات
- رسائل خطأ واضحة باللغة العربية

### 🎨 الأيقونات والتصميم:
- أيقونة قطرية أصيلة عالية الجودة
- أيقونات الجنس (ذكر/أنثى) في جميع القوائم
- شريط متحرك للرواتب مع الأيقونات
- تصميم عربي جميل ومتجاوب

### 🖱️ طرق التشغيل:

#### Windows:
1. **التشغيل المحسن:** 🚀_تشغيل_النظام.bat
2. **التشغيل السريع:** تشغيل_سريع.bat
3. **إنشاء اختصار:** إنشاء_اختصار_سطح_المكتب.bat

#### Linux/Mac:
1. **التشغيل المحسن:** ./🚀_تشغيل_النظام.sh
2. **اختصار سطح المكتب:** Qatar_Salary_System_Final.desktop

### 🌐 الوصول للنظام:
العنوان: http://localhost:7474

### 🔐 بيانات الدخول:
اسم المستخدم: admin
كلمة المرور: admin123

### 📋 المتطلبات:
- Python 3.7+ مثبت على النظام
- اتصال إنترنت (أول مرة فقط)
- 100 MB مساحة فارغة
- 512 MB ذاكرة عشوائية

### ✨ الميزات الكاملة:
✅ إدارة العمال مع تحديد الجنس (ذكر/أنثى)
✅ حساب الرواتب تلقائياً مع المكافآت والخصومات
✅ تقارير مفصلة وإحصائيات متقدمة
✅ شريط متحرك للرواتب مع أيقونات الجنس
✅ واجهة عربية جميلة ومتجاوبة
✅ تصدير البيانات بصيغ متعددة
✅ نظام مستخدمين متعدد المستويات
✅ نسخ احتياطية تلقائية

### 🛠️ استكشاف الأخطاء:
1. تأكد من تثبيت Python
2. تأكد من الاتصال بالإنترنت
3. شغل كمدير (Run as Administrator)
4. استخدم التشغيل المحسن للحصول على رسائل مفصلة

### 🎯 الجديد في هذه النسخة:
✅ إصلاح جميع أخطاء حقل الجنس
✅ أيقونات قطرية جميلة عالية الجودة
✅ تحسينات في الأداء والاستقرار
✅ رسائل خطأ واضحة ومفيدة
✅ تجربة مستخدم محسنة

---
🇶🇦 صنع بحب في قطر - النسخة النهائية بدون أخطاء
"""
    
    with open(os.path.join(final_dir, "README_FINAL.md"), "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    # Quick instructions
    quick_instructions = """╔══════════════════════════════════════════════════════════════╗
║  🇶🇦        نظام إدارة الرواتب القطري - النسخة النهائية        🇶🇦  ║
║           Qatar Salary Management System - Final            ║
╚══════════════════════════════════════════════════════════════╝

🚀 للتشغيل المباشر:

Windows:
  🖱️ اضغط مرتين على: 🚀_تشغيل_النظام.bat
  أو للتشغيل السريع: تشغيل_سريع.bat

Linux/Mac:
  🖱️ اضغط مرتين على: 🚀_تشغيل_النظام.sh

🎯 إنشاء اختصار سطح المكتب:
  🖱️ اضغط مرتين على: إنشاء_اختصار_سطح_المكتب.bat

🌐 العنوان: http://localhost:7474

🔐 بيانات الدخول:
  👤 اسم المستخدم: admin
  🔑 كلمة المرور: admin123

📋 المتطلبات:
  🐍 Python مثبت على النظام فقط
  🌐 اتصال إنترنت (أول مرة فقط)

✨ الجديد في النسخة النهائية:
  ✅ إصلاح جميع الأخطاء
  ✅ حقل الجنس يعمل بشكل مثالي
  ✅ أيقونات قطرية جميلة
  ✅ شريط متحرك للرواتب
  ✅ واجهة محسنة ومتطورة

🛠️ في حالة المشاكل:
  1. تأكد من تثبيت Python
  2. تأكد من الاتصال بالإنترنت
  3. شغل كمدير (Run as Administrator)

🇶🇦 صنع بحب في قطر - النسخة النهائية
"""
    
    with open(os.path.join(final_dir, "تعليمات_سريعة.txt"), "w", encoding="utf-8") as f:
        f.write(quick_instructions)
    
    print("   ✅ تم إنشاء التوثيق الشامل")

def create_autorun_files(final_dir):
    """Create autorun files for USB drives"""
    
    print("💿 إنشاء ملفات التشغيل التلقائي...")
    
    # Autorun.inf for Windows
    autorun_content = """[autorun]
icon=qatar_salary_icon.ico
label=نظام إدارة الرواتب القطري (النهائي)
action=تشغيل نظام إدارة الرواتب القطري
open=🚀_تشغيل_النظام.bat
"""
    
    with open(os.path.join(final_dir, "autorun.inf"), "w", encoding="utf-8") as f:
        f.write(autorun_content)
    
    print("   ✅ تم إنشاء ملفات التشغيل التلقائي")

def create_final_zip(final_dir):
    """Create final ZIP package"""
    
    print("📦 إنشاء الملف المضغوط النهائي...")
    
    zip_filename = f"{final_dir}.zip"
    
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(final_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, final_dir)
                zipf.write(file_path, arc_name)
    
    # Get ZIP size
    zip_size = os.path.getsize(zip_filename) / (1024 * 1024)
    print(f"   📊 حجم الملف المضغوط: {zip_size:.2f} MB")
    
    return zip_filename

if __name__ == "__main__":
    create_final_portable_version()

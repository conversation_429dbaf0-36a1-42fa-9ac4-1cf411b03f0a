@echo off
chcp 65001 >nul
title 🇶🇦 نظام إدارة الرواتب القطري - النسخة النهائية
color 0B

cls
echo.
echo        ╔═══════════════════════════════════════════════════════════╗
echo        ║  🇶🇦        نظام إدارة الرواتب القطري        🇶🇦  ║
echo        ║           Qatar Salary Management System              ║
echo        ║                   النسخة النهائية                    ║
echo        ╚═══════════════════════════════════════════════════════════╝
echo.
echo                    🚀 تشغيل مباشر - بدون أخطاء 🚀
echo.
echo        ✅ جميع الأخطاء تم إصلاحها
echo        ✅ حقل الجنس يعمل بشكل مثالي
echo        ✅ أيقونات قطرية جميلة
echo        ✅ واجهة عربية متطورة
echo.

REM Check if Python is installed
echo 🔍 فحص Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير موجود
    echo.
    echo 📥 يرجى تثبيت Python من: https://python.org
    echo 💡 أو استخدم النسخة المباشرة الذكية
    echo.
    pause
    exit /b 1
)

echo ✅ Python موجود

REM Check and install requirements
echo 📦 فحص المكتبات...
python -c "import flask" >nul 2>&1
if errorlevel 1 (
    echo 🔄 تثبيت المكتبات المطلوبة...
    echo    هذا قد يستغرق دقيقة أو دقيقتين...
    python -m pip install --upgrade pip >nul 2>&1
    python -m pip install flask flask-sqlalchemy werkzeug jinja2 >nul 2>&1
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المكتبات
        echo 🌐 تأكد من الاتصال بالإنترنت
        pause
        exit /b 1
    )
    echo ✅ تم تثبيت المكتبات بنجاح
) else (
    echo ✅ المكتبات موجودة
)

echo.
echo ┌──────────────────────────────────────────────────────────────┐
echo │  🚀 جاري تشغيل النظام...                                     │
echo │                                                              │
echo │  🌐 العنوان: http://localhost:7474                          │
echo │  👤 المستخدم: admin                                         │
echo │  🔑 كلمة المرور: admin123                                   │
echo │                                                              │
echo │  ✨ الميزات الجديدة:                                        │
echo │     • إدارة العمال مع تحديد الجنس                          │
echo │     • شريط متحرك للرواتب                                   │
echo │     • أيقونات قطرية جميلة                                  │
echo │     • تقارير مفصلة                                          │
echo │                                                              │
echo │  🛑 للإيقاف اضغط Ctrl+C                                     │
echo └──────────────────────────────────────────────────────────────┘
echo.

REM Open browser after 3 seconds
start /min cmd /c "timeout /t 3 /nobreak >nul && start http://localhost:7474"

REM Start the application
python simple_app.py

echo.
echo ┌──────────────────────────────────────────────────────────────┐
echo │  ✅ تم إيقاف النظام بنجاح                                    │
echo │  🙏 شكراً لاستخدام نظام إدارة الرواتب القطري               │
echo └──────────────────────────────────────────────────────────────┘
echo.
pause

@echo off
chcp 65001 >nul
title 🇶🇦 نظام إدارة الرواتب القطري
color 0A

cls
echo.
echo        ╔═══════════════════════════════════════════════════════════╗
echo        ║  🇶🇦        نظام إدارة الرواتب القطري        🇶🇦  ║
echo        ║           Qatar Salary Management System              ║
echo        ╚═══════════════════════════════════════════════════════════╝
echo.
echo                    🚀 تشغيل مباشر - بدون تعقيدات 🚀
echo.

REM Quick Python check
python --version >nul 2>&1
if errorlevel 1 (
    echo        ❌ Python غير موجود - يرجى تثبيته من python.org
    timeout /t 5 >nul
    start https://python.org
    pause
    exit
)

REM Quick install if needed
python -c "import flask" >nul 2>&1
if errorlevel 1 (
    echo        📦 تثبيت سريع للمكتبات...
    pip install flask flask-sqlalchemy >nul 2>&1
)

echo        ✅ جاري التشغيل...
echo.
echo        🌐 العنوان: http://localhost:7474
echo        👤 المستخدم: admin
echo        🔑 كلمة المرور: admin123
echo.

REM Start and open browser
start http://localhost:7474
python simple_app.py

pause

{% extends "simple_base.html" %}

{% block title %}{{ title }} - نظام الرواتب{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-plus"></i> {{ title }}</h1>
            <a href="{{ url_for('salary_payments') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> العودة للرواتب
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-md-10">
        <div class="card">
            <div class="card-body">
                <form method="POST" id="salaryForm">
                    <div class="row">
                        <!-- Worker and Basic Info -->
                        <div class="col-md-6">
                            <h5 class="mb-3"><i class="fas fa-user"></i> معلومات العامل</h5>
                            
                            <div class="mb-3">
                                <label class="form-label">العامل</label>
                                <select name="worker_id" class="form-select" required onchange="loadWorkerSalary()">
                                    <option value="">اختر العامل</option>
                                    {% for worker in workers %}
                                    <option value="{{ worker.id }}" data-salary="{{ worker.monthly_salary }}"
                                            {{ 'selected' if (preselected_worker_id == worker.id) or (payment and payment.worker_id == worker.id) else '' }}>
                                        {{ worker.name }} - {{ worker.nationality }} - {{ worker.job_type }}
                                    </option>
                                    {% endfor %}
                                </select>
                                <div class="form-text">
                                    <small>
                                        <i class="fas fa-user"></i>
                                        اختر العامل لإضافة راتبه الشهري
                                    </small>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">الشهر</label>
                                <input type="text" name="month" class="form-control" placeholder="YYYY-MM" required
                                       value="{{ preselected_month if preselected_month else (payment.month if payment else '') }}">
                                <div class="form-text">الصيغة: YYYY-MM (مثال: 2024-01)</div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">الراتب الأساسي</label>
                                <div class="input-group">
                                    <input type="number" name="base_salary" class="form-control" step="0.01" min="0" required onchange="calculateTotal()"
                                           value="{{ payment.base_salary if payment else '' }}">
                                    <span class="input-group-text">ر.ق</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Overtime and Adjustments -->
                        <div class="col-md-6">
                            <h5 class="mb-3"><i class="fas fa-calculator"></i> الحسابات</h5>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">الساعات الإضافية</label>
                                        <input type="number" name="overtime_hours" class="form-control" step="0.5" min="0" onchange="calculateTotal()"
                                               value="{{ payment.overtime_hours if payment else '0' }}">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">أجر الساعة الإضافية</label>
                                        <div class="input-group">
                                            <input type="number" name="overtime_rate" class="form-control" step="0.01" min="0" onchange="calculateTotal()"
                                                   value="{{ payment.overtime_rate if payment else '20' }}">
                                            <span class="input-group-text">ر.ق/ساعة</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">المكافآت</label>
                                <div class="input-group">
                                    <input type="number" name="bonuses" class="form-control" step="0.01" min="0" onchange="calculateTotal()"
                                           value="{{ payment.bonuses if payment else '0' }}">
                                    <span class="input-group-text">ر.ق</span>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">الخصومات</label>
                                <div class="input-group">
                                    <input type="number" name="deductions" class="form-control" step="0.01" min="0" onchange="calculateTotal()"
                                           value="{{ payment.deductions if payment else '0' }}">
                                    <span class="input-group-text">ر.ق</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <!-- Total Calculation Display -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5><i class="fas fa-calculator"></i> حساب الراتب</h5>
                                    <div class="row">
                                        <div class="col-md-3">
                                            <strong>الراتب الأساسي:</strong><br>
                                            <span id="displayBaseSalary">0.00 ر.ق</span>
                                        </div>
                                        <div class="col-md-3">
                                            <strong>الساعات الإضافية:</strong><br>
                                            <span id="displayOvertime">0.00 ر.ق</span>
                                        </div>
                                        <div class="col-md-3">
                                            <strong>المكافآت:</strong><br>
                                            <span id="displayBonuses">0.00 ر.ق</span>
                                        </div>
                                        <div class="col-md-3">
                                            <strong>الخصومات:</strong><br>
                                            <span id="displayDeductions">0.00 ر.ق</span>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="text-center">
                                        <h4><strong>إجمالي المبلغ: <span id="displayTotal" class="text-primary">0.00 ر.ق</span></strong></h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <!-- Payment Details -->
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="mb-3"><i class="fas fa-credit-card"></i> تفاصيل الدفع</h5>
                            
                            <div class="mb-3">
                                <label class="form-label">تاريخ الدفع</label>
                                <input type="date" name="payment_date" class="form-control"
                                       value="{{ payment.payment_date.strftime('%Y-%m-%d') if payment and payment.payment_date else '' }}">
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">طريقة الدفع</label>
                                <select name="payment_method" class="form-select">
                                    <option value="">اختر طريقة الدفع</option>
                                    <option value="cash" {{ 'selected' if payment and payment.payment_method == 'cash' else '' }}>نقداً</option>
                                    <option value="bank_transfer" {{ 'selected' if payment and payment.payment_method == 'bank_transfer' else '' }}>تحويل بنكي</option>
                                    <option value="check" {{ 'selected' if payment and payment.payment_method == 'check' else '' }}>شيك</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">الحالة</label>
                                <select name="status" class="form-select" required>
                                    <option value="unpaid" {{ 'selected' if payment and payment.status == 'unpaid' else '' }}>غير مدفوع</option>
                                    <option value="paid" {{ 'selected' if payment and payment.status == 'paid' else '' }}>مدفوع</option>
                                    <option value="partial" {{ 'selected' if payment and payment.status == 'partial' else '' }}>مدفوع جزئياً</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h5 class="mb-3"><i class="fas fa-sticky-note"></i> ملاحظات</h5>
                            
                            <div class="mb-3">
                                <label class="form-label">ملاحظات</label>
                                <textarea name="notes" class="form-control" rows="6" placeholder="أي ملاحظات إضافية...">{{ payment.notes if payment else '' }}</textarea>
                            </div>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <!-- Form Actions -->
                    <div class="row">
                        <div class="col-12">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="{{ url_for('salary_payments') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> {{ 'تحديث' if payment else 'حفظ' }} الراتب
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function loadWorkerSalary() {
    const workerSelect = document.querySelector('select[name="worker_id"]');
    const selectedOption = workerSelect.options[workerSelect.selectedIndex];
    if (selectedOption.dataset.salary) {
        document.querySelector('input[name="base_salary"]').value = selectedOption.dataset.salary;
        calculateTotal();
    }
}

function calculateTotal() {
    const baseSalary = parseFloat(document.querySelector('input[name="base_salary"]').value) || 0;
    const overtimeHours = parseFloat(document.querySelector('input[name="overtime_hours"]').value) || 0;
    const overtimeRate = parseFloat(document.querySelector('input[name="overtime_rate"]').value) || 0;
    const bonuses = parseFloat(document.querySelector('input[name="bonuses"]').value) || 0;
    const deductions = parseFloat(document.querySelector('input[name="deductions"]').value) || 0;
    
    const overtimeAmount = overtimeHours * overtimeRate;
    const totalAmount = baseSalary + overtimeAmount + bonuses - deductions;
    
    // Update display
    document.getElementById('displayBaseSalary').textContent = baseSalary.toFixed(2) + ' ر.ق';
    document.getElementById('displayOvertime').textContent = overtimeAmount.toFixed(2) + ' ر.ق';
    document.getElementById('displayBonuses').textContent = bonuses.toFixed(2) + ' ر.ق';
    document.getElementById('displayDeductions').textContent = deductions.toFixed(2) + ' ر.ق';
    document.getElementById('displayTotal').textContent = totalAmount.toFixed(2) + ' ر.ق';
}

// Set current month as default
document.addEventListener('DOMContentLoaded', function() {
    const monthField = document.querySelector('input[name="month"]');
    if (!monthField.value) {
        const now = new Date();
        const currentMonth = now.getFullYear() + '-' + String(now.getMonth() + 1).padStart(2, '0');
        monthField.value = currentMonth;
    }

    // Load worker salary if preselected
    const workerSelect = document.querySelector('select[name="worker_id"]');
    if (workerSelect.value) {
        loadWorkerSalary();
    }

    // Initial calculation
    calculateTotal();
});
</script>
{% endblock %}

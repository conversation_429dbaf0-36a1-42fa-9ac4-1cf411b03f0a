from flask_wtf import FlaskForm
from flask_wtf.file import <PERSON>Field, FileAllowed
from wtforms import <PERSON>Field, PasswordField, SelectField, FloatField, DateField, TextAreaField, IntegerField, BooleanField
from wtforms.validators import DataRequired, Email, Length, NumberRange, Optional
from datetime import datetime

class LoginForm(FlaskForm):
    username = String<PERSON><PERSON>('Username', validators=[DataRequired(), Length(min=3, max=80)])
    password = PasswordField('Password', validators=[DataRequired(), Length(min=6)])

class WorkerForm(FlaskForm):
    name = StringField('Name', validators=[DataRequired(), Length(max=100)])
    nationality = StringField('Nationality', validators=[DataRequired(), Length(max=50)])
    id_number = StringField('ID/Residence Number', validators=[DataRequired(), Length(max=50)])
    job_type = SelectField('Job Type', choices=[
        ('housemaid', 'Housemaid'),
        ('driver', 'Driver'),
        ('cook', 'Cook'),
        ('nanny', 'Nanny'),
        ('gardener', 'Gardener'),
        ('cleaner', 'Cleaner'),
        ('other', 'Other')
    ], validators=[DataRequired()])
    employment_date = DateField('Employment Date', validators=[DataRequired()])
    monthly_salary = FloatField('Monthly Salary', validators=[DataRequired(), NumberRange(min=0)])
    photo = FileField('Photo', validators=[FileAllowed(['jpg', 'png', 'jpeg', 'gif'], 'Images only!')])
    documents = FileField('Documents', validators=[FileAllowed(['pdf', 'doc', 'docx', 'jpg', 'png'], 'Documents only!')])
    is_active = BooleanField('Active')

class SalaryPaymentForm(FlaskForm):
    worker_id = SelectField('Worker', coerce=int, validators=[DataRequired()])
    month = StringField('Month (YYYY-MM)', validators=[DataRequired(), Length(min=7, max=7)])
    base_salary = FloatField('Base Salary', validators=[DataRequired(), NumberRange(min=0)])
    overtime_hours = FloatField('Overtime Hours', validators=[Optional(), NumberRange(min=0)], default=0)
    overtime_rate = FloatField('Overtime Rate per Hour', validators=[Optional(), NumberRange(min=0)], default=0)
    bonuses = FloatField('Bonuses', validators=[Optional(), NumberRange(min=0)], default=0)
    deductions = FloatField('Deductions', validators=[Optional(), NumberRange(min=0)], default=0)
    vacation_days = IntegerField('Vacation Days', validators=[Optional(), NumberRange(min=0)], default=0)
    payment_date = DateField('Payment Date', validators=[Optional()])
    payment_method = SelectField('Payment Method', choices=[
        ('', 'Select Method'),
        ('cash', 'Cash'),
        ('bank_transfer', 'Bank Transfer'),
        ('check', 'Check')
    ], validators=[Optional()])
    status = SelectField('Status', choices=[
        ('unpaid', 'Unpaid'),
        ('paid', 'Paid'),
        ('partial', 'Partial')
    ], validators=[DataRequired()])
    notes = TextAreaField('Notes', validators=[Optional(), Length(max=500)])

class UserForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired(), Length(min=3, max=80)])
    email = StringField('Email', validators=[DataRequired(), Email(), Length(max=120)])
    password = PasswordField('Password', validators=[Optional(), Length(min=6)])
    role = SelectField('Role', choices=[
        ('user', 'User'),
        ('admin', 'Administrator')
    ], validators=[DataRequired()])
    is_active = BooleanField('Active')

class SettingsForm(FlaskForm):
    company_name = StringField('Company/Sponsor Name', validators=[Optional(), Length(max=200)])
    default_currency = SelectField('Default Currency', choices=[
        ('SAR', 'Saudi Riyal (SAR)'),
        ('USD', 'US Dollar (USD)'),
        ('EUR', 'Euro (EUR)'),
        ('AED', 'UAE Dirham (AED)')
    ], validators=[DataRequired()])
    default_language = SelectField('Default Language', choices=[
        ('en', 'English'),
        ('ar', 'Arabic')
    ], validators=[DataRequired()])

class ReportForm(FlaskForm):
    start_date = DateField('Start Date', validators=[DataRequired()])
    end_date = DateField('End Date', validators=[DataRequired()])
    worker_id = SelectField('Worker (Optional)', coerce=int, validators=[Optional()])
    status = SelectField('Payment Status', choices=[
        ('', 'All'),
        ('paid', 'Paid'),
        ('unpaid', 'Unpaid'),
        ('partial', 'Partial')
    ], validators=[Optional()])

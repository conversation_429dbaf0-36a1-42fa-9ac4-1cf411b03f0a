#!/usr/bin/env python3
"""
Create a fixed portable version with all bug fixes and icons
"""

import os
import shutil
import zipfile
from pathlib import Path

def create_fixed_portable_version():
    """Create the fixed portable version"""

    print("🔧 إنشاء النسخة المحمولة المُصححة...")

    # Create fixed portable directory
    fixed_dir = "Qatar_Salary_System_Fixed_Portable"
    if os.path.exists(fixed_dir):
        shutil.rmtree(fixed_dir)
    os.makedirs(fixed_dir)

    # Copy application files
    copy_application_files(fixed_dir)

    # Copy icons
    copy_icons(fixed_dir)

    # Create enhanced launchers
    create_enhanced_launchers(fixed_dir)

    # Create desktop shortcut scripts
    create_desktop_shortcuts(fixed_dir)

    # Create documentation
    create_documentation(fixed_dir)

    # Create autorun files
    create_autorun_files(fixed_dir)

    # Test the application
    test_application(fixed_dir)

    # Create ZIP package
    create_zip_package(fixed_dir)

    print("🎉 تم إنشاء النسخة المحمولة المُصححة بنجاح!")

def copy_application_files(fixed_dir):
    """Copy all application files"""

    print("📁 نسخ ملفات التطبيق...")

    # Files to copy
    files_to_copy = [
        "simple_app.py",
        "requirements.txt"
    ]

    # Directories to copy
    dirs_to_copy = [
        "templates",
        "static"
    ]

    # Copy files
    for file in files_to_copy:
        if os.path.exists(file):
            shutil.copy2(file, fixed_dir)
            print(f"   ✅ تم نسخ: {file}")
        else:
            print(f"   ⚠️ الملف غير موجود: {file}")

    # Copy directories
    for dir_name in dirs_to_copy:
        if os.path.exists(dir_name):
            dest_dir = os.path.join(fixed_dir, dir_name)
            if os.path.exists(dest_dir):
                shutil.rmtree(dest_dir)
            shutil.copytree(dir_name, dest_dir)
            print(f"   ✅ تم نسخ: {dir_name}")
        else:
            print(f"   ⚠️ المجلد غير موجود: {dir_name}")

def copy_icons(fixed_dir):
    """Copy icon files"""

    print("🖼️ نسخ الأيقونات...")

    # Icon files to copy
    icon_sources = [
        ("Qatar_Salary_System_Portable/qatar_salary_icon.ico", "qatar_salary_icon.ico"),
        ("Qatar_Salary_System_Portable/qatar_salary_icon.png", "qatar_salary_icon.png"),
        ("Qatar_Salary_System_Final_Portable/qatar_salary_icon.ico", "qatar_salary_icon.ico"),
        ("Qatar_Salary_System_Final_Portable/qatar_salary_icon.png", "qatar_salary_icon.png")
    ]

    icons_copied = False
    for source, dest_name in icon_sources:
        if os.path.exists(source):
            dest_file = os.path.join(fixed_dir, dest_name)
            shutil.copy2(source, dest_file)
            print(f"   ✅ تم نسخ: {dest_name}")
            icons_copied = True
            break

    if not icons_copied:
        print("   ⚠️ لم يتم العثور على الأيقونات - سيتم إنشاؤها")
        create_default_icons(fixed_dir)

def create_default_icons(fixed_dir):
    """Create default icon files if not found"""

    print("   🎨 إنشاء أيقونات افتراضية...")

    # Create a simple text file as placeholder
    icon_placeholder = """# Qatar Salary System Icon
# This is a placeholder for the Qatar salary system icon
# The actual icon should be a 256x256 PNG or ICO file
# with Qatar flag colors and salary/money symbols
"""

    with open(os.path.join(fixed_dir, "qatar_salary_icon.txt"), "w", encoding="utf-8") as f:
        f.write(icon_placeholder)

    print("   ✅ تم إنشاء ملف نائب للأيقونة")

def create_enhanced_launchers(fixed_dir):
    """Create enhanced launcher scripts"""

    print("🚀 إنشاء ملفات التشغيل المحسنة...")

    # Enhanced Windows launcher with error handling
    enhanced_launcher = """@echo off
chcp 65001 >nul 2>&1
title 🇶🇦 نظام إدارة الرواتب القطري - النسخة المُصححة
color 0B

cls
echo.
echo        ╔═══════════════════════════════════════════════════════════╗
echo        ║  🇶🇦        نظام إدارة الرواتب القطري        🇶🇦  ║
echo        ║           Qatar Salary Management System              ║
echo        ║                   النسخة المُصححة                    ║
echo        ╚═══════════════════════════════════════════════════════════╝
echo.
echo                    🚀 تشغيل مباشر - بدون أخطاء 🚀
echo.
echo        ✅ تم إصلاح جميع الأخطاء
echo        ✅ حقل الجنس يعمل بشكل مثالي
echo        ✅ أيقونات قطرية جميلة
echo        ✅ واجهة عربية متطورة
echo        ✅ معالجة شاملة للأخطاء
echo.

REM Check if Python is installed
echo 🔍 فحص Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير موجود
    echo.
    echo 📥 يرجى تثبيت Python من: https://python.org
    echo 💡 أو استخدم النسخة المباشرة الذكية
    echo.
    echo 🔗 تحميل Python:
    echo    https://www.python.org/downloads/
    echo.
    pause
    start https://python.org
    exit /b 1
)

echo ✅ Python موجود

REM Check and install requirements
echo 📦 فحص المكتبات...
python -c "import flask, flask_sqlalchemy, werkzeug" >nul 2>&1
if errorlevel 1 (
    echo 🔄 تثبيت المكتبات المطلوبة...
    echo    هذا قد يستغرق دقيقة أو دقيقتين...
    echo.

    python -m pip install --upgrade pip >nul 2>&1
    if errorlevel 1 (
        echo ⚠️ تحديث pip فشل - المتابعة...
    )

    echo    📦 تثبيت Flask...
    python -m pip install flask >nul 2>&1
    if errorlevel 1 (
        echo ❌ فشل في تثبيت Flask
        echo 🌐 تأكد من الاتصال بالإنترنت
        pause
        exit /b 1
    )

    echo    📦 تثبيت Flask-SQLAlchemy...
    python -m pip install flask-sqlalchemy >nul 2>&1
    if errorlevel 1 (
        echo ❌ فشل في تثبيت Flask-SQLAlchemy
        echo 🌐 تأكد من الاتصال بالإنترنت
        pause
        exit /b 1
    )

    echo    📦 تثبيت Werkzeug...
    python -m pip install werkzeug >nul 2>&1
    if errorlevel 1 (
        echo ❌ فشل في تثبيت Werkzeug
        echo 🌐 تأكد من الاتصال بالإنترنت
        pause
        exit /b 1
    )

    echo ✅ تم تثبيت جميع المكتبات بنجاح
) else (
    echo ✅ جميع المكتبات موجودة
)

echo.
echo ┌──────────────────────────────────────────────────────────────┐
echo │  🚀 جاري تشغيل النظام...                                     │
echo │                                                              │
echo │  🌐 العنوان: http://localhost:7474                          │
echo │  👤 المستخدم: admin                                         │
echo │  🔑 كلمة المرور: admin123                                   │
echo │                                                              │
echo │  ✨ الميزات الجديدة:                                        │
echo │     • إدارة العمال مع تحديد الجنس (بدون أخطاء)             │
echo │     • شريط متحرك للرواتب مع الأيقونات                      │
echo │     • أيقونات قطرية جميلة                                  │
echo │     • تقارير مفصلة ومتطورة                                 │
echo │     • معالجة شاملة للأخطاء                                  │
echo │                                                              │
echo │  🛑 للإيقاف اضغط Ctrl+C                                     │
echo └──────────────────────────────────────────────────────────────┘
echo.

REM Test the application first
echo 🧪 اختبار التطبيق...
python -c "import simple_app; print('✅ التطبيق جاهز للتشغيل')" 2>nul
if errorlevel 1 (
    echo ❌ خطأ في التطبيق
    echo 📝 تفاصيل الخطأ:
    python -c "import simple_app"
    echo.
    pause
    exit /b 1
)

REM Open browser after 3 seconds
start /min cmd /c "timeout /t 3 /nobreak >nul && start http://localhost:7474"

REM Start the application
python simple_app.py

echo.
echo ┌──────────────────────────────────────────────────────────────┐
echo │  ✅ تم إيقاف النظام بنجاح                                    │
echo │  🙏 شكراً لاستخدام نظام إدارة الرواتب القطري               │
echo │  🇶🇦 النسخة المُصححة - بدون أخطاء                          │
echo └──────────────────────────────────────────────────────────────┘
echo.
pause
"""

    with open(os.path.join(fixed_dir, "🚀_تشغيل_النظام_المُصحح.bat"), "w", encoding="utf-8") as f:
        f.write(enhanced_launcher)

    print("   ✅ تم إنشاء ملف التشغيل المحسن")

    # Quick launcher
    quick_launcher = """@echo off
chcp 65001 >nul
title نظام الرواتب القطري - النسخة المُصححة

echo 🇶🇦 نظام إدارة الرواتب القطري 🇶🇦
echo النسخة المُصححة - بدون أخطاء
echo.

python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير موجود - شغل "🚀_تشغيل_النظام_المُصحح.bat"
    pause
    exit
)

python -c "import flask" >nul 2>&1
if errorlevel 1 (
    echo 📦 تثبيت المكتبات...
    pip install flask flask-sqlalchemy werkzeug >nul
)

echo ✅ جاري التشغيل...
echo 🌐 http://localhost:7474
echo 👤 admin / 🔑 admin123

start http://localhost:7474
python simple_app.py
pause
"""

    with open(os.path.join(fixed_dir, "تشغيل_سريع.bat"), "w", encoding="utf-8") as f:
        f.write(quick_launcher)

    # Linux/Mac launcher
    unix_launcher = """#!/bin/bash

clear
echo ""
echo "    ╔═══════════════════════════════════════════════════════════╗"
echo "    ║  🇶🇦        نظام إدارة الرواتب القطري        🇶🇦  ║"
echo "    ║           Qatar Salary Management System              ║"
echo "    ║                   النسخة المُصححة                    ║"
echo "    ╚═══════════════════════════════════════════════════════════╝"
echo ""
echo "                🚀 تشغيل مباشر - بدون أخطاء 🚀"
echo ""
echo "    ✅ تم إصلاح جميع الأخطاء"
echo "    ✅ حقل الجنس يعمل بشكل مثالي"
echo "    ✅ أيقونات قطرية جميلة"
echo "    ✅ واجهة عربية متطورة"
echo ""

# Find Python
if command -v python3 &> /dev/null; then
    PYTHON="python3"
elif command -v python &> /dev/null; then
    PYTHON="python"
else
    echo "    ❌ Python غير موجود - يرجى تثبيته"
    echo "    📥 Ubuntu/Debian: sudo apt install python3 python3-pip"
    echo "    📥 CentOS/RHEL: sudo yum install python3 python3-pip"
    echo "    📥 macOS: brew install python3"
    exit 1
fi

echo "    ✅ Python موجود ($PYTHON)"

# Check and install requirements
$PYTHON -c "import flask, flask_sqlalchemy, werkzeug" &> /dev/null
if [ $? -ne 0 ]; then
    echo "    📦 تثبيت المكتبات..."
    $PYTHON -m pip install flask flask-sqlalchemy werkzeug &> /dev/null
    if [ $? -ne 0 ]; then
        echo "    ❌ فشل في تثبيت المكتبات"
        echo "    🌐 تأكد من الاتصال بالإنترنت"
        exit 1
    fi
    echo "    ✅ تم تثبيت المكتبات"
else
    echo "    ✅ جميع المكتبات موجودة"
fi

echo ""
echo "    🧪 اختبار التطبيق..."
$PYTHON -c "import simple_app; print('    ✅ التطبيق جاهز للتشغيل')" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "    ❌ خطأ في التطبيق"
    echo "    📝 تفاصيل الخطأ:"
    $PYTHON -c "import simple_app"
    exit 1
fi

echo ""
echo "    🚀 جاري تشغيل النظام..."
echo ""
echo "    🌐 العنوان: http://localhost:7474"
echo "    👤 المستخدم: admin"
echo "    🔑 كلمة المرور: admin123"
echo ""

# Open browser
if command -v xdg-open &> /dev/null; then
    xdg-open http://localhost:7474 &
elif command -v open &> /dev/null; then
    open http://localhost:7474 &
fi

# Start application
$PYTHON simple_app.py

echo ""
echo "    ✅ تم إيقاف النظام"
"""

    launcher_path = os.path.join(fixed_dir, "🚀_تشغيل_النظام_المُصحح.sh")
    with open(launcher_path, "w", encoding="utf-8") as f:
        f.write(unix_launcher)

    # Make executable
    try:
        os.chmod(launcher_path, 0o755)
    except:
        pass

    print("   ✅ تم إنشاء جميع ملفات التشغيل")

def create_desktop_shortcuts(fixed_dir):
    """Create desktop shortcut scripts"""

    print("🔗 إنشاء سكريپتات الاختصارات...")

    # Windows desktop shortcut
    shortcut_script = """@echo off
chcp 65001 >nul
title إنشاء اختصار سطح المكتب - النسخة المُصححة

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║            إنشاء اختصار على سطح المكتب - النسخة المُصححة      ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

set "current_dir=%~dp0"
set "desktop=%USERPROFILE%\\Desktop"
set "shortcut_name=🇶🇦 نظام الرواتب القطري (مُصحح).lnk"

echo 🔧 جاري إنشاء الاختصار...

REM Create VBS script
echo Set oWS = WScript.CreateObject("WScript.Shell") > temp_shortcut.vbs
echo sLinkFile = "%desktop%\\%shortcut_name%" >> temp_shortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> temp_shortcut.vbs
echo oLink.TargetPath = "%current_dir%🚀_تشغيل_النظام_المُصحح.bat" >> temp_shortcut.vbs
echo oLink.WorkingDirectory = "%current_dir%" >> temp_shortcut.vbs
if exist "%current_dir%qatar_salary_icon.ico" (
    echo oLink.IconLocation = "%current_dir%qatar_salary_icon.ico" >> temp_shortcut.vbs
)
echo oLink.Description = "نظام إدارة الرواتب القطري - النسخة المُصححة بدون أخطاء" >> temp_shortcut.vbs
echo oLink.Save >> temp_shortcut.vbs

REM Execute VBS script
cscript //nologo temp_shortcut.vbs

REM Clean up
del temp_shortcut.vbs

if exist "%desktop%\\%shortcut_name%" (
    echo ✅ تم إنشاء الاختصار بنجاح على سطح المكتب!
    echo 🎯 يمكنك الآن تشغيل النظام من سطح المكتب
    echo 🚀 النسخة المُصححة - بدون أخطاء
    echo 🎨 مع الأيقونات القطرية الجميلة
) else (
    echo ❌ فشل في إنشاء الاختصار
)

echo.
pause
"""

    with open(os.path.join(fixed_dir, "إنشاء_اختصار_سطح_المكتب.bat"), "w", encoding="utf-8") as f:
        f.write(shortcut_script)

    print("   ✅ تم إنشاء سكريپت اختصار Windows")

def create_documentation(fixed_dir):
    """Create comprehensive documentation"""

    print("📖 إنشاء التوثيق...")

    # Main README
    readme_content = """# نظام إدارة الرواتب القطري - النسخة المُصححة
# Qatar Salary Management System - Fixed Version

## 🚀 النسخة المُصححة - بدون أخطاء

### ✅ تم إصلاح جميع الأخطاء:
- حقل الجنس يعمل بشكل مثالي (بدون أخطاء قاعدة البيانات)
- معالجة شاملة للاستثناءات مع rollback تلقائي
- رسائل خطأ واضحة ومفيدة باللغة العربية
- قيم افتراضية آمنة لجميع الحقول المطلوبة
- اختبار شامل قبل التشغيل

### 🎨 الأيقونات والتصميم:
- أيقونة قطرية أصيلة عالية الجودة
- أيقونات الجنس (ذكر/أنثى) في جميع القوائم
- شريط متحرك للرواتب مع الأيقونات الملونة
- تصميم عربي جميل ومتجاوب

### 🖱️ طرق التشغيل:

#### Windows:
1. **التشغيل المحسن:** 🚀_تشغيل_النظام_المُصحح.bat
2. **التشغيل السريع:** تشغيل_سريع.bat
3. **إنشاء اختصار:** إنشاء_اختصار_سطح_المكتب.bat

#### Linux/Mac:
1. **التشغيل المحسن:** ./🚀_تشغيل_النظام_المُصحح.sh

### 🌐 الوصول للنظام:
العنوان: http://localhost:7474

### 🔐 بيانات الدخول:
اسم المستخدم: admin
كلمة المرور: admin123

### 📋 المتطلبات:
- Python 3.7+ مثبت على النظام
- اتصال إنترنت (أول مرة فقط)
- 100 MB مساحة فارغة
- 512 MB ذاكرة عشوائية

### ✨ الميزات الكاملة:
✅ إدارة العمال مع تحديد الجنس (ذكر/أنثى) - بدون أخطاء
✅ حساب الرواتب تلقائياً مع المكافآت والخصومات
✅ تقارير مفصلة وإحصائيات متقدمة
✅ شريط متحرك للرواتب مع أيقونات الجنس الملونة
✅ واجهة عربية جميلة ومتجاوبة مع جميع الشاشات
✅ تصدير البيانات بصيغ متعددة
✅ نظام مستخدمين متعدد المستويات
✅ معالجة شاملة للأخطاء مع رسائل واضحة

### 🛠️ استكشاف الأخطاء:
1. تأكد من تثبيت Python (python --version)
2. تأكد من الاتصال بالإنترنت
3. شغل كمدير (Run as Administrator)
4. استخدم التشغيل المحسن للحصول على رسائل مفصلة
5. تحقق من رسائل الخطأ في النافذة

### 🎯 الجديد في النسخة المُصححة:
✅ إصلاح جميع أخطاء حقل الجنس نهائياً
✅ معالجة شاملة للجلسات المعلقة
✅ اختبار تلقائي للتطبيق قبل التشغيل
✅ رسائل خطأ مفصلة ومفيدة
✅ تحسينات في الأداء والاستقرار
✅ دعم أفضل لجميع أنظمة التشغيل

---
🇶🇦 صنع بحب في قطر - النسخة المُصححة بدون أخطاء
"""

    with open(os.path.join(fixed_dir, "README_FIXED.md"), "w", encoding="utf-8") as f:
        f.write(readme_content)

    print("   ✅ تم إنشاء التوثيق الرئيسي")

def create_autorun_files(fixed_dir):
    """Create autorun files for USB drives"""

    print("💿 إنشاء ملفات التشغيل التلقائي...")

    # Autorun.inf for Windows
    autorun_content = """[autorun]
icon=qatar_salary_icon.ico
label=نظام إدارة الرواتب القطري (مُصحح)
action=تشغيل نظام إدارة الرواتب القطري - النسخة المُصححة
open=🚀_تشغيل_النظام_المُصحح.bat
"""

    with open(os.path.join(fixed_dir, "autorun.inf"), "w", encoding="utf-8") as f:
        f.write(autorun_content)

    print("   ✅ تم إنشاء ملف التشغيل التلقائي")

def test_application(fixed_dir):
    """Test the application for errors"""

    print("🧪 اختبار التطبيق...")

    try:
        # Change to the fixed directory
        original_dir = os.getcwd()
        os.chdir(fixed_dir)

        # Try to import the application
        import sys
        sys.path.insert(0, '.')

        try:
            import simple_app
            print("   ✅ تم تحميل التطبيق بنجاح")

            # Test database models
            try:
                with simple_app.app.app_context():
                    simple_app.db.create_all()
                    print("   ✅ قاعدة البيانات جاهزة")
            except Exception as e:
                print(f"   ⚠️ تحذير في قاعدة البيانات: {e}")

        except Exception as e:
            print(f"   ❌ خطأ في التطبيق: {e}")
            return False

        finally:
            # Restore original directory
            os.chdir(original_dir)
            # Remove from sys.path
            if '.' in sys.path:
                sys.path.remove('.')

        return True

    except Exception as e:
        print(f"   ❌ خطأ في الاختبار: {e}")
        return False

def create_zip_package(fixed_dir):
    """Create ZIP package for the fixed version"""

    print("📦 إنشاء الملف المضغوط...")

    zip_filename = f"{fixed_dir}.zip"

    # Remove existing ZIP if exists
    if os.path.exists(zip_filename):
        os.remove(zip_filename)

    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(fixed_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, fixed_dir)
                zipf.write(file_path, arc_name)

    # Get ZIP size
    zip_size = os.path.getsize(zip_filename) / (1024 * 1024)
    print(f"   📊 حجم الملف المضغوط: {zip_size:.2f} MB")

    return zip_filename

if __name__ == "__main__":
    create_fixed_portable_version()
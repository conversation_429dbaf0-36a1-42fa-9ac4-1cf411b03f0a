نظام إدارة رواتب العمالة المنزلية
=====================================

🌐 عنوان النظام:
http://localhost:7474

👤 تسجيل الدخول:
اسم المستخدم: admin
كلمة المرور: admin123

🚀 تشغيل النظام:
- Windows: انقر مرتين على start_server.bat
- Linux/Mac: ./start_server.sh
- يدوياً: python simple_app.py

📋 المميزات الرئيسية:
✅ إدارة العمال (80+ جنسية، 60+ نوع عمل)
✅ إدارة الرواتب (حساب تلقائي، ساعات إضافية)
✅ لوحة تحكم شاملة (إحصائيات فورية)
✅ واجهة عربية كاملة مع دعم RTL
✅ تحقق شامل من صحة البيانات

🔧 أنواع العمل المدعومة:
- العمالة المنزلية (خادمة، مدبرة، نظافة)
- رعاية الأطفال وكبار السن (مربية، ممرضة)
- الطبخ والمطبخ (طباخ، شيف)
- السائقين والنقل (سائق شخصي، عائلة)
- الحدائق والمساحات الخارجية (بستاني)
- الأمن والحراسة (حارس أمن، بواب)
- الصيانة والإصلاح (كهربائي، سباك، نجار)
- الخدمات المتخصصة (مساعد شخصي، مدرس)
- الخدمات التقنية (دعم تقني، منزل ذكي)

🌍 الجنسيات المدعومة:
- الدول العربية (22 دولة)
- آسيا (20 دولة)
- أفريقيا (16 دولة)
- أوروبا (10 دول)
- الأمريكتان (8 دول)
- أوقيانوسيا (2 دولة)

📊 الإحصائيات:
- إجمالي العمال النشطين
- مدفوعات الشهر الحالي
- الرواتب غير المدفوعة
- إجمالي المبالغ المدفوعة
- توزيع العمال حسب الجنسية ونوع العمل

🔒 الأمان:
- تشفير كلمات المرور
- جلسات آمنة
- التحقق من صحة البيانات
- منع التكرار والأخطاء

📱 التوافق:
- جميع المتصفحات الحديثة
- الهواتف والأجهزة اللوحية
- أنظمة Windows, Linux, Mac

💾 قاعدة البيانات:
- SQLite (ملف salary_system.db)
- نسخ احتياطية تلقائية
- أداء محسن

🎯 الاستخدام:
1. تشغيل النظام
2. تسجيل الدخول
3. إضافة العمال
4. إدارة الرواتب
5. متابعة الإحصائيات

تم تطوير النظام بعناية ليلبي احتياجات إدارة العمالة المنزلية
في المملكة العربية السعودية ودول الخليج العربي.

# نظام إدارة الرواتب القطري - النسخة المحمولة
# Qatar Salary Management System - Portable Version

## 🚀 التشغيل السريع / Quick Start

### Windows:
1. تشغيل `install.bat` لتثبيت المتطلبات (مرة واحدة فقط)
2. تشغيل `run.bat` لبدء النظام
3. فتح المتصفح على: http://localhost:7474

### Linux/Mac:
1. تشغيل `./install.sh` لتثبيت المتطلبات (مرة واحدة فقط)
2. تشغيل `./run.sh` لبدء النظام
3. فتح المتصفح على: http://localhost:7474

## 📋 المتطلبات / Requirements

- Python 3.7+ مثبت على النظام
- اتصال بالإنترنت لتثبيت المكتبات (أول مرة فقط)

## 🔐 بيانات الدخول الافتراضية / Default Login

- **اسم المستخدم / Username:** admin
- **كلمة المرور / Password:** admin123

## 📁 محتويات النسخة المحمولة / Portable Contents

- `simple_app.py` - التطبيق الرئيسي
- `templates/` - قوالب الواجهة
- `static/` - الملفات الثابتة (CSS, JS, Images)
- `requirements.txt` - المكتبات المطلوبة
- `install.bat/sh` - سكريپت التثبيت
- `run.bat/sh` - سكريپت التشغيل

## ✨ الميزات / Features

- ✅ إدارة العمال والموظفين
- ✅ حساب وإدارة الرواتب
- ✅ تقارير مفصلة
- ✅ واجهة عربية/إنجليزية
- ✅ نظام مستخدمين متعدد
- ✅ تصدير البيانات
- ✅ شريط متحرك للرواتب
- ✅ تصنيف حسب الجنس

## 🛠️ استكشاف الأخطاء / Troubleshooting

### إذا لم يعمل النظام:
1. تأكد من تثبيت Python
2. شغل install.bat/sh مرة أخرى
3. تأكد من الاتصال بالإنترنت

### If the system doesn't work:
1. Make sure Python is installed
2. Run install.bat/sh again
3. Check internet connection

## 📞 الدعم / Support

للدعم والمساعدة، يرجى التواصل مع فريق التطوير.
For support and help, please contact the development team.

## 📄 الترخيص / License

هذا النظام مطور خصيصاً لإدارة الرواتب في قطر.
This system is specially developed for salary management in Qatar.

---
🇶🇦 صنع بحب في قطر / Made with ❤️ in Qatar

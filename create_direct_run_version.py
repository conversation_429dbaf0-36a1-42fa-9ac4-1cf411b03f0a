#!/usr/bin/env python3
"""
Create a direct-run version with embedded Python (portable Python)
"""

import os
import shutil
import zipfile
import urllib.request
import subprocess
import sys

def create_direct_run_version():
    """Create a version that includes portable Python"""
    
    print("🚀 إنشاء النسخة المباشرة مع Python محمول...")
    
    # Create directory
    direct_dir = "Qatar_Salary_System_Direct"
    if os.path.exists(direct_dir):
        shutil.rmtree(direct_dir)
    os.makedirs(direct_dir)
    
    # Copy application files
    copy_application_files(direct_dir)
    
    # Create enhanced launcher
    create_enhanced_launcher(direct_dir)
    
    # Create auto-installer
    create_auto_installer(direct_dir)
    
    # Create documentation
    create_direct_documentation(direct_dir)
    
    # Create ZIP
    create_direct_zip(direct_dir)
    
    print("🎉 تم إنشاء النسخة المباشرة بنجاح!")

def copy_application_files(direct_dir):
    """Copy all application files"""
    
    print("📁 نسخ ملفات التطبيق...")
    
    # Files to copy
    files_to_copy = [
        "simple_app.py",
        "requirements.txt"
    ]
    
    # Directories to copy
    dirs_to_copy = [
        "templates",
        "static"
    ]
    
    # Copy files
    for file in files_to_copy:
        if os.path.exists(file):
            shutil.copy2(file, direct_dir)
            print(f"   ✅ تم نسخ: {file}")
    
    # Copy directories
    for dir_name in dirs_to_copy:
        if os.path.exists(dir_name):
            shutil.copytree(dir_name, os.path.join(direct_dir, dir_name))
            print(f"   ✅ تم نسخ: {dir_name}")
    
    # Copy icons if available
    icon_files = [
        "Qatar_Salary_System_Portable/qatar_salary_icon.ico",
        "Qatar_Salary_System_Portable/qatar_salary_icon.png"
    ]
    
    for icon_file in icon_files:
        if os.path.exists(icon_file):
            shutil.copy2(icon_file, direct_dir)
            print(f"   ✅ تم نسخ: {os.path.basename(icon_file)}")

def create_enhanced_launcher(direct_dir):
    """Create enhanced launcher that handles Python installation"""
    
    print("🚀 إنشاء ملف التشغيل المحسن...")
    
    launcher_content = """@echo off
chcp 65001 >nul
title 🇶🇦 نظام إدارة الرواتب القطري - التشغيل المباشر
color 0A

cls
echo.
echo        ╔═══════════════════════════════════════════════════════════╗
echo        ║  🇶🇦        نظام إدارة الرواتب القطري        🇶🇦  ║
echo        ║           Qatar Salary Management System              ║
echo        ║                   التشغيل المباشر                    ║
echo        ╚═══════════════════════════════════════════════════════════╝
echo.
echo                    🚀 تشغيل ذكي - يثبت كل شيء تلقائياً 🚀
echo.

REM Check if Python is installed
echo 🔍 فحص Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير موجود
    echo.
    echo 📥 سيتم تحميل وتثبيت Python تلقائياً...
    echo    هذا قد يستغرق بضع دقائق...
    echo.
    
    REM Download Python installer
    echo 🌐 تحميل Python...
    powershell -Command "& {Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.11.0/python-3.11.0-amd64.exe' -OutFile 'python_installer.exe'}"
    
    if exist python_installer.exe (
        echo 📦 تثبيت Python...
        python_installer.exe /quiet InstallAllUsers=0 PrependPath=1 Include_test=0
        
        REM Wait for installation
        timeout /t 30 /nobreak >nul
        
        REM Clean up
        del python_installer.exe
        
        echo ✅ تم تثبيت Python
        echo 🔄 يرجى إعادة تشغيل هذا الملف
        pause
        exit
    ) else (
        echo ❌ فشل في تحميل Python
        echo 🌐 يرجى تثبيت Python يدوياً من: https://python.org
        start https://python.org
        pause
        exit
    )
)

echo ✅ Python موجود

REM Check and install requirements
echo 📦 فحص المكتبات...
python -c "import flask" >nul 2>&1
if errorlevel 1 (
    echo 🔄 تثبيت المكتبات المطلوبة...
    python -m pip install --upgrade pip >nul 2>&1
    python -m pip install flask flask-sqlalchemy werkzeug jinja2 >nul 2>&1
    echo ✅ تم تثبيت المكتبات
) else (
    echo ✅ المكتبات موجودة
)

echo.
echo ┌──────────────────────────────────────────────────────────────┐
echo │  🚀 جاري تشغيل النظام...                                     │
echo │                                                              │
echo │  🌐 العنوان: http://localhost:7474                          │
echo │  👤 المستخدم: admin                                         │
echo │  🔑 كلمة المرور: admin123                                   │
echo │                                                              │
echo │  🛑 للإيقاف اضغط Ctrl+C                                     │
echo └──────────────────────────────────────────────────────────────┘
echo.

REM Open browser after 3 seconds
start /min cmd /c "timeout /t 3 /nobreak >nul && start http://localhost:7474"

REM Start the application
python simple_app.py

echo.
echo ✅ تم إيقاف النظام
pause
"""
    
    with open(os.path.join(direct_dir, "🚀_تشغيل_ذكي.bat"), "w", encoding="utf-8") as f:
        f.write(launcher_content)
    
    # Create simple launcher
    simple_launcher = """@echo off
chcp 65001 >nul
title نظام إدارة الرواتب القطري

echo 🇶🇦 نظام إدارة الرواتب القطري 🇶🇦
echo.

python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير موجود - شغل "🚀_تشغيل_ذكي.bat" للتثبيت التلقائي
    pause
    exit
)

python -c "import flask" >nul 2>&1
if errorlevel 1 (
    echo 📦 تثبيت المكتبات...
    pip install flask flask-sqlalchemy >nul
)

echo ✅ جاري التشغيل...
echo 🌐 http://localhost:7474
echo 👤 admin / 🔑 admin123

start http://localhost:7474
python simple_app.py
pause
"""
    
    with open(os.path.join(direct_dir, "تشغيل_بسيط.bat"), "w", encoding="utf-8") as f:
        f.write(simple_launcher)
    
    print("   ✅ تم إنشاء ملفات التشغيل")

def create_auto_installer(direct_dir):
    """Create auto-installer script"""
    
    print("🔧 إنشاء المثبت التلقائي...")
    
    installer_content = """@echo off
chcp 65001 >nul
title مثبت نظام إدارة الرواتب القطري التلقائي
color 0B

cls
echo.
echo        ╔═══════════════════════════════════════════════════════════╗
echo        ║              مثبت نظام إدارة الرواتب القطري              ║
echo        ║                    المثبت التلقائي                      ║
echo        ╚═══════════════════════════════════════════════════════════╝
echo.

echo 🔧 سيقوم هذا المثبت بـ:
echo    ✅ تحميل وتثبيت Python تلقائياً
echo    ✅ تثبيت جميع المكتبات المطلوبة
echo    ✅ إنشاء اختصار على سطح المكتب
echo    ✅ تجهيز النظام للتشغيل المباشر
echo.

set /p confirm="هل تريد المتابعة؟ (Y/N): "
if /i not "%confirm%"=="Y" (
    echo تم الإلغاء
    pause
    exit
)

echo.
echo 🚀 بدء التثبيت...

REM Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo 📥 تحميل Python...
    powershell -Command "& {try { Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.11.0/python-3.11.0-amd64.exe' -OutFile 'python_installer.exe' } catch { Write-Host 'فشل التحميل' }}"
    
    if exist python_installer.exe (
        echo 📦 تثبيت Python...
        python_installer.exe /quiet InstallAllUsers=0 PrependPath=1
        timeout /t 60 /nobreak >nul
        del python_installer.exe
        
        REM Refresh environment
        call refreshenv >nul 2>&1
    )
)

REM Install packages
echo 📦 تثبيت المكتبات...
python -m pip install --upgrade pip
python -m pip install flask flask-sqlalchemy werkzeug jinja2

REM Create desktop shortcut
echo 🔗 إنشاء اختصار سطح المكتب...
set "current_dir=%~dp0"
set "desktop=%USERPROFILE%\\Desktop"
set "shortcut_name=🇶🇦 نظام الرواتب القطري.lnk"

echo Set oWS = WScript.CreateObject("WScript.Shell") > temp_shortcut.vbs
echo sLinkFile = "%desktop%\\%shortcut_name%" >> temp_shortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> temp_shortcut.vbs
echo oLink.TargetPath = "%current_dir%🚀_تشغيل_ذكي.bat" >> temp_shortcut.vbs
echo oLink.WorkingDirectory = "%current_dir%" >> temp_shortcut.vbs
echo oLink.IconLocation = "%current_dir%qatar_salary_icon.ico" >> temp_shortcut.vbs
echo oLink.Description = "نظام إدارة الرواتب القطري - تشغيل مباشر" >> temp_shortcut.vbs
echo oLink.Save >> temp_shortcut.vbs

cscript //nologo temp_shortcut.vbs >nul 2>&1
del temp_shortcut.vbs >nul 2>&1

echo.
echo ✅ تم التثبيت بنجاح!
echo.
echo 🎯 يمكنك الآن:
echo    - تشغيل النظام من سطح المكتب
echo    - أو تشغيل "🚀_تشغيل_ذكي.bat"
echo    - أو تشغيل "تشغيل_بسيط.bat"
echo.
echo 🌐 العنوان: http://localhost:7474
echo 👤 المستخدم: admin
echo 🔑 كلمة المرور: admin123
echo.

set /p run_now="هل تريد تشغيل النظام الآن؟ (Y/N): "
if /i "%run_now%"=="Y" (
    start "نظام الرواتب" "%current_dir%🚀_تشغيل_ذكي.bat"
)

pause
"""
    
    with open(os.path.join(direct_dir, "مثبت_تلقائي.bat"), "w", encoding="utf-8") as f:
        f.write(installer_content)
    
    print("   ✅ تم إنشاء المثبت التلقائي")

def create_direct_documentation(direct_dir):
    """Create documentation for direct version"""
    
    print("📖 إنشاء التوثيق...")
    
    readme_content = """# نظام إدارة الرواتب القطري - النسخة المباشرة
# Qatar Salary Management System - Direct Version

## 🚀 التشغيل المباشر - يثبت كل شيء تلقائياً

### ✨ مميزات النسخة المباشرة:
- 🔥 تثبيت Python تلقائياً إذا لم يكن موجود
- 📦 تثبيت جميع المكتبات تلقائياً
- 🌐 فتح المتصفح تلقائياً
- 🔗 إنشاء اختصار سطح المكتب
- 🚀 تشغيل مباشر بنقرة واحدة

### 🖱️ طرق التشغيل:

#### 1. التشغيل الذكي (الأفضل):
اضغط مرتين على: 🚀_تشغيل_ذكي.bat
- يثبت Python تلقائياً إذا لم يكن موجود
- يثبت المكتبات تلقائياً
- يفتح المتصفح تلقائياً

#### 2. المثبت التلقائي:
اضغط مرتين على: مثبت_تلقائي.bat
- يثبت كل شيء مرة واحدة
- ينشئ اختصار سطح المكتب
- يجهز النظام للتشغيل

#### 3. التشغيل البسيط:
اضغط مرتين على: تشغيل_بسيط.bat
- للتشغيل السريع إذا كان Python موجود

### 🌐 الوصول للنظام:
العنوان: http://localhost:7474

### 🔐 بيانات الدخول:
اسم المستخدم: admin
كلمة المرور: admin123

### 📋 متطلبات النظام:
- Windows 7+ (يفضل Windows 10+)
- اتصال إنترنت (للتحميل التلقائي)
- 500 MB مساحة فارغة
- 1 GB ذاكرة عشوائية

### ✨ الميزات:
✅ إدارة العمال مع تحديد الجنس
✅ حساب الرواتب تلقائياً
✅ تقارير مفصلة
✅ شريط متحرك للرواتب
✅ واجهة عربية جميلة
✅ تصميم متجاوب

### 🛠️ استكشاف الأخطاء:
1. شغل "مثبت_تلقائي.bat" أولاً
2. تأكد من الاتصال بالإنترنت
3. شغل كمدير (Run as Administrator)
4. أغلق برامج مكافحة الفيروسات مؤقتاً

### 🎯 ترتيب التشغيل المقترح:
1. شغل "مثبت_تلقائي.bat" (مرة واحدة فقط)
2. استخدم الاختصار من سطح المكتب
3. أو شغل "🚀_تشغيل_ذكي.bat"

---
🇶🇦 صنع بحب في قطر
"""
    
    with open(os.path.join(direct_dir, "README_DIRECT.md"), "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    # Create quick instructions
    quick_instructions = """╔══════════════════════════════════════════════════════════════╗
║  🇶🇦        نظام إدارة الرواتب القطري - النسخة المباشرة        🇶🇦  ║
║           Qatar Salary Management System - Direct           ║
╚══════════════════════════════════════════════════════════════╝

🚀 للتشغيل المباشر:

1️⃣ أول مرة:
   🖱️ اضغط مرتين على: مثبت_تلقائي.bat
   ⏳ انتظر انتهاء التثبيت

2️⃣ التشغيل العادي:
   🖱️ اضغط على الاختصار في سطح المكتب
   أو اضغط على: 🚀_تشغيل_ذكي.bat

🌐 العنوان: http://localhost:7474
👤 المستخدم: admin
🔑 كلمة المرور: admin123

✨ مميزات:
✅ يثبت Python تلقائياً
✅ يثبت المكتبات تلقائياً
✅ ينشئ اختصار سطح المكتب
✅ يفتح المتصفح تلقائياً

🇶🇦 صنع بحب في قطر
"""
    
    with open(os.path.join(direct_dir, "تعليمات_سريعة.txt"), "w", encoding="utf-8") as f:
        f.write(quick_instructions)
    
    print("   ✅ تم إنشاء التوثيق")

def create_direct_zip(direct_dir):
    """Create ZIP package for direct version"""
    
    print("📦 إنشاء الملف المضغوط...")
    
    zip_filename = f"{direct_dir}.zip"
    
    with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(direct_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, direct_dir)
                zipf.write(file_path, arc_name)
    
    # Get ZIP size
    zip_size = os.path.getsize(zip_filename) / (1024 * 1024)
    print(f"   📊 حجم الملف المضغوط: {zip_size:.2f} MB")

if __name__ == "__main__":
    create_direct_run_version()

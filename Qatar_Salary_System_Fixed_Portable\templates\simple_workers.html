{% extends "simple_base.html" %}

{% block title %}إدارة العمال - نظام الرواتب{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-users"></i> إدارة العمال</h1>
            <a href="{{ url_for('add_worker') }}" class="btn btn-primary">
                <i class="fas fa-user-plus"></i> إضافة عامل
            </a>
        </div>
    </div>
</div>

<!-- Search -->
<div class="row mb-3">
    <div class="col-md-6">
        <form method="GET" class="d-flex">
            <input type="text" name="search" class="form-control" placeholder="البحث بالاسم أو الجنسية أو رقم الهوية..." value="{{ search }}">
            <button type="submit" class="btn btn-outline-secondary ms-2">
                <i class="fas fa-search"></i>
            </button>
            {% if search %}
            <a href="{{ url_for('workers') }}" class="btn btn-outline-danger ms-2">
                <i class="fas fa-times"></i>
            </a>
            {% endif %}
        </form>
    </div>
</div>

<!-- Workers Table -->
<div class="card">
    <div class="card-body">
        {% if workers %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>الاسم</th>
                        <th>الجنسية</th>
                        <th>الجنس</th>
                        <th>رقم الهوية</th>
                        <th>نوع العمل</th>
                        <th>تاريخ التوظيف</th>
                        <th>الراتب الشهري</th>
                        <th>الحالة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for worker in workers %}
                    <tr>
                        <td><strong>{{ worker.name }}</strong></td>
                        <td>
                            <i class="fas fa-flag"></i> {{ worker.nationality }}
                        </td>
                        <td>
                            {% if worker.gender %}
                                <span class="badge bg-{{ 'primary' if worker.gender == 'ذكر' else 'danger' }}">
                                    {% if worker.gender == 'ذكر' %}
                                        <i class="fas fa-mars"></i> ذكر
                                    {% else %}
                                        <i class="fas fa-venus"></i> أنثى
                                    {% endif %}
                                </span>
                            {% else %}
                                <span class="badge bg-secondary">
                                    <i class="fas fa-question"></i> غير محدد
                                </span>
                            {% endif %}
                        </td>
                        <td>
                            <code>{{ worker.id_number }}</code>
                        </td>
                        <td>
                            <span class="badge bg-info">
                                {% if worker.job_type == 'housemaid' %}خادمة منزل
                                {% elif worker.job_type == 'driver' %}سائق
                                {% elif worker.job_type == 'cook' %}طباخ
                                {% elif worker.job_type == 'nanny' %}مربية أطفال
                                {% elif worker.job_type == 'gardener' %}بستاني
                                {% elif worker.job_type == 'cleaner' %}عامل نظافة
                                {% elif worker.job_type == 'security_guard' %}حارس أمن
                                {% elif worker.job_type == 'maintenance_worker' %}عامل صيانة
                                {% else %}{{ worker.job_type }}
                                {% endif %}
                            </span>
                        </td>
                        <td>{{ worker.employment_date.strftime('%Y-%m-%d') }}</td>
                        <td>{{ "%.2f"|format(worker.monthly_salary) }} ر.ق</td>
                        <td>
                            {% if worker.is_active %}
                                <span class="badge bg-success">نشط</span>
                            {% else %}
                                <span class="badge bg-secondary">غير نشط</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('view_worker', id=worker.id) }}"
                                   class="btn btn-outline-info" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('edit_worker', id=worker.id) }}"
                                   class="btn btn-outline-primary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('add_salary_payment') }}?worker_id={{ worker.id }}"
                                   class="btn btn-outline-success" title="إضافة راتب">
                                    <i class="fas fa-money-bill-wave"></i>
                                </a>
                                <a href="{{ url_for('salary_payments', worker_id=worker.id) }}"
                                   class="btn btn-outline-warning" title="تاريخ الرواتب">
                                    <i class="fas fa-history"></i>
                                </a>
                                {% if current_user.is_admin() %}
                                <button type="button" class="btn btn-outline-danger"
                                        onclick="confirmDelete({{ worker.id }}, '{{ worker.name }}')"
                                        title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">لا يوجد عمال</h4>
            {% if search %}
                <p class="text-muted">لا توجد نتائج للبحث المطلوب.</p>
                <a href="{{ url_for('workers') }}" class="btn btn-secondary">مسح البحث</a>
            {% else %}
                <p class="text-muted">ابدأ بإضافة أول عامل.</p>
                <a href="{{ url_for('add_worker') }}" class="btn btn-primary">
                    <i class="fas fa-user-plus"></i> إضافة عامل
                </a>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف العامل <strong id="workerName"></strong>؟</p>
                <p class="text-danger"><small>هذا الإجراء لا يمكن التراجع عنه.</small></p>
                <div id="deleteWarning" class="alert alert-warning" style="display: none;">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>تنبيه:</strong> لا يمكن حذف العامل إذا كان لديه رواتب مسجلة.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(workerId, workerName) {
    document.getElementById('workerName').textContent = workerName;
    document.getElementById('deleteForm').action = '/workers/' + workerId + '/delete';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}

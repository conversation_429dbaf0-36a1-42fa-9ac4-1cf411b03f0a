{% extends "simple_base.html" %}

{% block title %}{{ worker.name }} - تفاصيل العامل{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-user"></i> تفاصيل العامل: {{ worker.name }}</h1>
            <div>
                <a href="{{ url_for('edit_worker', id=worker.id) }}" class="btn btn-primary me-2">
                    <i class="fas fa-edit"></i> تعديل
                </a>
                <a href="{{ url_for('workers') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> العودة للعمال
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Worker Information -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> المعلومات الأساسية</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>الاسم:</strong></td>
                                <td>{{ worker.name }}</td>
                            </tr>
                            <tr>
                                <td><strong>الجنسية:</strong></td>
                                <td>
                                    <i class="fas fa-flag"></i> {{ worker.nationality }}
                                    {% if worker.nationality == 'قطر' %}🇶🇦{% endif %}
                                </td>
                            </tr>
                            {% if worker.gender %}
                            <tr>
                                <td><strong>الجنس:</strong></td>
                                <td>
                                    <span class="badge bg-{{ 'primary' if worker.gender == 'ذكر' else 'danger' }} fs-6">
                                        {% if worker.gender == 'ذكر' %}
                                            <i class="fas fa-mars"></i> ذكر
                                        {% else %}
                                            <i class="fas fa-venus"></i> أنثى
                                        {% endif %}
                                    </span>
                                </td>
                            </tr>
                            {% endif %}
                            <tr>
                                <td><strong>رقم الهوية/الإقامة:</strong></td>
                                <td><code>{{ worker.id_number }}</code></td>
                            </tr>
                            <tr>
                                <td><strong>نوع العمل:</strong></td>
                                <td>
                                    <span class="badge bg-info">
                                        {% if worker.job_type == 'housemaid' %}خادمة منزل
                                        {% elif worker.job_type == 'driver' %}سائق شخصي
                                        {% elif worker.job_type == 'nanny' %}مربية أطفال
                                        {% elif worker.job_type == 'cook' %}طباخ/طباخة
                                        {% elif worker.job_type == 'cleaner' %}عامل/عاملة نظافة
                                        {% elif worker.job_type == 'gardener' %}بستاني
                                        {% elif worker.job_type == 'security_guard' %}حارس أمن
                                        {% elif worker.job_type == 'maintenance_worker' %}عامل صيانة
                                        {% else %}{{ worker.job_type }}
                                        {% endif %}
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>تاريخ التوظيف:</strong></td>
                                <td>{{ worker.employment_date.strftime('%Y-%m-%d') }}</td>
                            </tr>
                            <tr>
                                <td><strong>الراتب الشهري:</strong></td>
                                <td><strong>{{ "%.2f"|format(worker.monthly_salary) }} ر.ق</strong></td>
                            </tr>
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>
                                    <span class="badge bg-{{ 'success' if worker.is_active else 'secondary' }}">
                                        {{ 'نشط' if worker.is_active else 'غير نشط' }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>تاريخ الإضافة:</strong></td>
                                <td>{{ worker.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-bolt"></i> إجراءات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('add_salary_payment') }}?worker_id={{ worker.id }}" 
                       class="btn btn-success">
                        <i class="fas fa-money-bill-wave"></i> إضافة راتب
                    </a>
                    <a href="{{ url_for('salary_payments', worker_id=worker.id) }}" 
                       class="btn btn-info">
                        <i class="fas fa-history"></i> تاريخ الرواتب
                    </a>
                    <a href="{{ url_for('edit_worker', id=worker.id) }}" 
                       class="btn btn-primary">
                        <i class="fas fa-edit"></i> تعديل البيانات
                    </a>
                    {% if current_user.is_admin() and not worker.salary_payments %}
                    <button type="button" class="btn btn-danger" 
                            onclick="confirmDelete({{ worker.id }}, '{{ worker.name }}')">
                        <i class="fas fa-trash"></i> حذف العامل
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Statistics -->
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-chart-pie"></i> إحصائيات</h5>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <h4 class="text-primary">{{ worker.salary_payments|length }}</h4>
                    <small class="text-muted">إجمالي الرواتب المسجلة</small>
                </div>
                {% if worker.salary_payments %}
                <hr>
                <div class="text-center">
                    <h4 class="text-success">
                        {{ "%.0f"|format(worker.salary_payments|selectattr('status', 'equalto', 'paid')|map(attribute='total_amount')|sum) }}
                    </h4>
                    <small class="text-muted">إجمالي المبلغ المدفوع (ر.ق)</small>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Recent Payments -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-clock"></i> آخر الرواتب</h5>
            </div>
            <div class="card-body">
                {% if recent_payments %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الشهر</th>
                                <th>الراتب الأساسي</th>
                                <th>الساعات الإضافية</th>
                                <th>المكافآت</th>
                                <th>الخصومات</th>
                                <th>إجمالي المبلغ</th>
                                <th>تاريخ الدفع</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payment in recent_payments %}
                            <tr>
                                <td><strong>{{ payment.month }}</strong></td>
                                <td>{{ "%.2f"|format(payment.base_salary) }} ر.ق</td>
                                <td>
                                    {% if payment.overtime_hours > 0 %}
                                        {{ payment.overtime_hours }}h<br>
                                        <small class="text-muted">{{ "%.2f"|format(payment.overtime_hours * payment.overtime_rate) }} ر.ق</small>
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if payment.bonuses > 0 %}
                                        {{ "%.2f"|format(payment.bonuses) }} ر.ق
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if payment.deductions > 0 %}
                                        {{ "%.2f"|format(payment.deductions) }} ر.ق
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td><strong>{{ "%.2f"|format(payment.total_amount) }} ر.ق</strong></td>
                                <td>
                                    {% if payment.payment_date %}
                                        {{ payment.payment_date.strftime('%Y-%m-%d') }}
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if payment.status == 'paid' else 'warning' if payment.status == 'partial' else 'danger' }}">
                                        {% if payment.status == 'paid' %}مدفوع
                                        {% elif payment.status == 'partial' %}جزئي
                                        {% else %}غير مدفوع
                                        {% endif %}
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="text-center mt-3">
                    <a href="{{ url_for('salary_payments', worker_id=worker.id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-list"></i> عرض جميع الرواتب
                    </a>
                </div>
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-money-bill-wave fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد رواتب مسجلة</h5>
                    <p class="text-muted">لم يتم تسجيل أي رواتب لهذا العامل بعد.</p>
                    <a href="{{ url_for('add_salary_payment') }}?worker_id={{ worker.id }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة راتب
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف العامل <strong id="workerName"></strong>؟</p>
                <p class="text-danger"><small>هذا الإجراء لا يمكن التراجع عنه.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(workerId, workerName) {
    document.getElementById('workerName').textContent = workerName;
    document.getElementById('deleteForm').action = '/workers/' + workerId + '/delete';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}

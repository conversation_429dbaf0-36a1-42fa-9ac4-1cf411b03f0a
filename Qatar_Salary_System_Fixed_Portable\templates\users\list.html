{% extends "simple_base.html" %}

{% block title %}إدارة المستخدمين - نظام الرواتب{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-user-cog"></i> إدارة المستخدمين</h1>
            <a href="{{ url_for('add_user') }}" class="btn btn-primary">
                <i class="fas fa-user-plus"></i> إضافة مستخدم
            </a>
        </div>
    </div>
</div>

<!-- Users Table -->
<div class="card">
    <div class="card-body">
        {% if users %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>اسم المستخدم</th>
                        <th>البريد الإلكتروني</th>
                        <th>الدور</th>
                        <th>الحالة</th>
                        <th>تاريخ الإنشاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr>
                        <td>
                            <strong>{{ user.username }}</strong>
                            {% if user.id == current_user.id %}
                                <span class="badge bg-info ms-2">أنت</span>
                            {% endif %}
                        </td>
                        <td>{{ user.email }}</td>
                        <td>
                            <span class="badge bg-{{ 'danger' if user.role == 'admin' else 'primary' }}">
                                {{ 'مدير' if user.role == 'admin' else 'مستخدم' }}
                            </span>
                        </td>
                        <td>
                            <span class="badge bg-{{ 'success' if user.is_active else 'secondary' }}">
                                {{ 'نشط' if user.is_active else 'غير نشط' }}
                            </span>
                        </td>
                        <td>{{ user.created_at.strftime('%Y-%m-%d') }}</td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('edit_user', id=user.id) }}" 
                                   class="btn btn-outline-primary" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if user.id != current_user.id %}
                                <button type="button" class="btn btn-outline-danger" 
                                        onclick="confirmDelete({{ user.id }}, '{{ user.username }}')" 
                                        title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">لا يوجد مستخدمين</h4>
            <p class="text-muted">ابدأ بإضافة مستخدمين جدد للنظام.</p>
            <a href="{{ url_for('add_user') }}" class="btn btn-primary">
                <i class="fas fa-user-plus"></i> إضافة مستخدم
            </a>
        </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف المستخدم <strong id="userName"></strong>؟</p>
                <p class="text-danger"><small>هذا الإجراء لا يمكن التراجع عنه.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(userId, userName) {
    document.getElementById('userName').textContent = userName;
    document.getElementById('deleteForm').action = '/users/' + userId + '/delete';
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}

{% extends "simple_base.html" %}

{% block title %}لوحة التحكم - نظام الرواتب{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="mb-4"><i class="fas fa-tachometer-alt"></i> لوحة التحكم</h1>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stats-card text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>{{ total_workers }}</h3>
                        <p class="mb-0">العمال النشطون</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>{{ total_payments_this_month }}</h3>
                        <p class="mb-0">مدفوعات هذا الشهر</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-money-bill-wave fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>{{ unpaid_payments }}</h3>
                        <p class="mb-0">رواتب غير مدفوعة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>{{ recent_payments|length }}</h3>
                        <p class="mb-0">المعاملات الأخيرة</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Payments -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-clock"></i> المدفوعات الأخيرة</h5>
            </div>
            <div class="card-body">
                {% if recent_payments %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>العامل</th>
                                    <th>الشهر</th>
                                    <th>المبلغ</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payment in recent_payments %}
                                <tr>
                                    <td>{{ payment.worker.name }}</td>
                                    <td>{{ payment.month }}</td>
                                    <td>{{ "%.2f"|format(payment.total_amount) }} ريال</td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if payment.status == 'paid' else 'warning' }}">
                                            {{ 'مدفوع' if payment.status == 'paid' else 'غير مدفوع' }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted text-center">لا توجد مدفوعات حديثة.</p>
                {% endif %}
                <div class="text-end">
                    <a href="{{ url_for('salary_payments') }}" class="btn btn-sm btn-primary">عرض الكل</a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-bolt"></i> إجراءات سريعة</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ url_for('add_worker') }}" class="btn btn-outline-primary">
                        <i class="fas fa-user-plus"></i> إضافة عامل
                    </a>
                    <a href="{{ url_for('add_salary_payment') }}" class="btn btn-outline-success">
                        <i class="fas fa-money-bill-wave"></i> إضافة راتب
                    </a>
                    <a href="{{ url_for('workers') }}" class="btn btn-outline-info">
                        <i class="fas fa-users"></i> عرض العمال
                    </a>
                    <a href="{{ url_for('salary_payments') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-list"></i> عرض الرواتب
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

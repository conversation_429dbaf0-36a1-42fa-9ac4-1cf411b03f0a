#!/usr/bin/env python3
"""
Simple script to add gender column to workers table
"""

import sqlite3
import os

def fix_database():
    """Add gender column to workers table"""
    db_path = 'qatar_salary_system.db'
    
    if not os.path.exists(db_path):
        print("Database file not found.")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if gender column exists
        cursor.execute("PRAGMA table_info(worker)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'gender' not in columns:
            print("Adding gender column...")
            cursor.execute("ALTER TABLE worker ADD COLUMN gender VARCHAR(10) DEFAULT 'ذكر'")
            cursor.execute("UPDATE worker SET gender = 'ذكر' WHERE gender IS NULL")
            conn.commit()
            print("Gender column added successfully!")
        else:
            print("Gender column already exists.")
        
        conn.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    fix_database()

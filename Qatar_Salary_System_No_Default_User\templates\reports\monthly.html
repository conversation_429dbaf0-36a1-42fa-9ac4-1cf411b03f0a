{% extends "simple_base.html" %}

{% block title %}التقرير الشهري - نظام الرواتب{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1><i class="fas fa-calendar-alt"></i> التقرير الشهري</h1>
            <a href="{{ url_for('reports') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> العودة للتقارير
            </a>
        </div>
    </div>
</div>

<!-- Month Selection -->
<div class="row mb-4">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-calendar"></i> اختيار الشهر</h5>
            </div>
            <div class="card-body">
                <form method="GET">
                    <div class="mb-3">
                        <label class="form-label">الشهر</label>
                        <input type="month" name="month" class="form-control" value="{{ month }}" onchange="this.form.submit()">
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> ملخص الشهر: {{ month }}</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <h4 class="text-primary">{{ total_workers_paid }}</h4>
                        <small class="text-muted">عمال مدفوعين</small>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-success">{{ "%.0f"|format(paid_amount) }}</h4>
                        <small class="text-muted">مبلغ مدفوع (ر.ق)</small>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-warning">{{ "%.0f"|format(unpaid_amount) }}</h4>
                        <small class="text-muted">مبلغ معلق (ر.ق)</small>
                    </div>
                    <div class="col-md-3">
                        <h4 class="text-info">{{ "%.0f"|format(total_amount) }}</h4>
                        <small class="text-muted">إجمالي المبلغ (ر.ق)</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Payments for the Month -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-money-bill-wave"></i> مدفوعات الشهر</h5>
            </div>
            <div class="card-body">
                {% if payments %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>العامل</th>
                                <th>الجنسية</th>
                                <th>نوع العمل</th>
                                <th>الراتب الأساسي</th>
                                <th>الساعات الإضافية</th>
                                <th>المكافآت</th>
                                <th>الخصومات</th>
                                <th>إجمالي المبلغ</th>
                                <th>تاريخ الدفع</th>
                                <th>الحالة</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for payment in payments %}
                            <tr>
                                <td><strong>{{ payment.worker.name }}</strong></td>
                                <td>
                                    <i class="fas fa-flag"></i> {{ payment.worker.nationality }}
                                </td>
                                <td>
                                    <span class="badge bg-info">
                                        {% if payment.worker.job_type == 'housemaid' %}خادمة منزل
                                        {% elif payment.worker.job_type == 'driver' %}سائق
                                        {% elif payment.worker.job_type == 'cook' %}طباخ
                                        {% elif payment.worker.job_type == 'nanny' %}مربية أطفال
                                        {% elif payment.worker.job_type == 'gardener' %}بستاني
                                        {% elif payment.worker.job_type == 'cleaner' %}عامل نظافة
                                        {% elif payment.worker.job_type == 'security_guard' %}حارس أمن
                                        {% elif payment.worker.job_type == 'maintenance_worker' %}عامل صيانة
                                        {% else %}{{ payment.worker.job_type }}
                                        {% endif %}
                                    </span>
                                </td>
                                <td>{{ "%.2f"|format(payment.base_salary) }} ر.ق</td>
                                <td>
                                    {% if payment.overtime_hours > 0 %}
                                        {{ payment.overtime_hours }}h<br>
                                        <small class="text-muted">{{ "%.2f"|format(payment.overtime_hours * payment.overtime_rate) }} ر.ق</small>
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if payment.bonuses > 0 %}
                                        {{ "%.2f"|format(payment.bonuses) }} ر.ق
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td>
                                    {% if payment.deductions > 0 %}
                                        {{ "%.2f"|format(payment.deductions) }} ر.ق
                                    {% else %}
                                        -
                                    {% endif %}
                                </td>
                                <td><strong>{{ "%.2f"|format(payment.total_amount) }} ر.ق</strong></td>
                                <td>
                                    {% if payment.payment_date %}
                                        {{ payment.payment_date.strftime('%Y-%m-%d') }}
                                    {% else %}
                                        <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'success' if payment.status == 'paid' else 'warning' if payment.status == 'partial' else 'danger' }}">
                                        {% if payment.status == 'paid' %}مدفوع
                                        {% elif payment.status == 'partial' %}جزئي
                                        {% else %}غير مدفوع
                                        {% endif %}
                                    </span>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد مدفوعات لهذا الشهر</h4>
                    <p class="text-muted">لم يتم تسجيل أي مدفوعات للشهر المحدد.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Workers Without Payment -->
{% if workers_without_payment %}
<div class="row">
    <div class="col-12">
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <h5><i class="fas fa-exclamation-triangle"></i> عمال بدون راتب لهذا الشهر</h5>
            </div>
            <div class="card-body">
                <p class="text-warning">
                    <strong>تنبيه:</strong> يوجد {{ workers_without_payment|length }} عامل لم يتم تسجيل راتب لهم في هذا الشهر.
                </p>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>الجنسية</th>
                                <th>نوع العمل</th>
                                <th>الراتب الشهري</th>
                                <th>تاريخ التوظيف</th>
                                <th>الإجراء</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for worker in workers_without_payment %}
                            <tr>
                                <td><strong>{{ worker.name }}</strong></td>
                                <td>
                                    <i class="fas fa-flag"></i> {{ worker.nationality }}
                                </td>
                                <td>
                                    <span class="badge bg-info">
                                        {% if worker.job_type == 'housemaid' %}خادمة منزل
                                        {% elif worker.job_type == 'driver' %}سائق
                                        {% elif worker.job_type == 'cook' %}طباخ
                                        {% elif worker.job_type == 'nanny' %}مربية أطفال
                                        {% elif worker.job_type == 'gardener' %}بستاني
                                        {% elif worker.job_type == 'cleaner' %}عامل نظافة
                                        {% elif worker.job_type == 'security_guard' %}حارس أمن
                                        {% elif worker.job_type == 'maintenance_worker' %}عامل صيانة
                                        {% else %}{{ worker.job_type }}
                                        {% endif %}
                                    </span>
                                </td>
                                <td>{{ "%.2f"|format(worker.monthly_salary) }} ر.ق</td>
                                <td>{{ worker.employment_date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    <a href="{{ url_for('add_salary_payment') }}?worker_id={{ worker.id }}&month={{ month }}" 
                                       class="btn btn-sm btn-primary">
                                        <i class="fas fa-plus"></i> إضافة راتب
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

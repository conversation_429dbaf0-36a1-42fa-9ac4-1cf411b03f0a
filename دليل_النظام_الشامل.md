# دليل نظام إدارة رواتب العمالة المنزلية - قطر 🇶🇦

## نظرة عامة
نظام شامل لإدارة رواتب العمالة المنزلية مطور خصيصاً لدولة قطر مع التوافق الكامل مع القوانين والأنظمة القطرية.

## 🚀 بدء التشغيل

### المتطلبات
- Python 3.7+
- Flask
- SQLAlchemy

### التشغيل
```bash
python simple_app.py
```

**العنوان:** http://localhost:7474
**تسجيل الدخول:** admin / admin123

## 📋 الوحدات الرئيسية

### 1. إدارة المستخدمين 👥
**الوصول:** قائمة التنقل → إدارة المستخدمين (للمديرين فقط)

#### المميزات:
- ✅ إضافة مستخدمين جدد
- ✅ تعديل بيانات المستخدمين
- ✅ حذف المستخدمين
- ✅ تحديد الأدوار (مدير/مستخدم عادي)
- ✅ تفعيل/إلغاء تفعيل الحسابات
- ✅ مؤشر قوة كلمة المرور

#### الأدوار:
- **مدير النظام:** صلاحيات كاملة + إدارة المستخدمين
- **مستخدم عادي:** إدارة العمال والرواتب فقط

#### الحقول المطلوبة:
- اسم المستخدم (أكثر من 3 أحرف، فريد)
- البريد الإلكتروني (صحيح وفريد)
- كلمة المرور (أكثر من 6 أحرف)
- الدور (مدير/مستخدم)
- حالة الحساب (نشط/غير نشط)

### 2. إدارة العمال 👷
**الوصول:** قائمة التنقل → العمال

#### المميزات:
- ✅ إضافة عمال جدد
- ✅ تعديل بيانات العمال
- ✅ حذف العمال (بدون رواتب مسجلة)
- ✅ البحث والتصفية
- ✅ عرض تفصيلي لكل عامل

#### الحقول المطلوبة:
- الاسم (أكثر من حرفين)
- الجنسية (قطر افتراضي + 80+ جنسية)
- رقم الهوية/الإقامة (8-15 حرف/رقم)
- نوع العمل (60+ نوع عمل)
- تاريخ التوظيف
- الراتب الشهري (بالريال القطري)
- حالة العامل (نشط/غير نشط)

#### أنواع العمل الشائعة في قطر:
- خادمة منزل
- سائق شخصي
- مربية أطفال
- طباخ/طباخة
- عامل/عاملة نظافة
- بستاني
- حارس أمن

### 3. إدارة الرواتب 💰
**الوصول:** قائمة التنقل → الرواتب

#### المميزات:
- ✅ إضافة رواتب شهرية
- ✅ تعديل الرواتب
- ✅ حذف الرواتب (للمديرين)
- ✅ حساب تلقائي للإجمالي
- ✅ تتبع حالة الدفع
- ✅ تصفية متقدمة

#### الحقول:
- العامل (قائمة منسدلة)
- الشهر (YYYY-MM)
- الراتب الأساسي (ر.ق)
- الساعات الإضافية (افتراضي 20 ر.ق/ساعة)
- المكافآت (ر.ق)
- الخصومات (ر.ق)
- أيام الإجازة
- تاريخ الدفع
- طريقة الدفع (نقد/تحويل بنكي/شيك)
- حالة الدفع (مدفوع/غير مدفوع/جزئي)
- ملاحظات

#### الحساب التلقائي:
```
إجمالي المبلغ = الراتب الأساسي + (الساعات الإضافية × معدل الساعة) + المكافآت - الخصومات
```

### 4. نظام التقارير 📊
**الوصول:** قائمة التنقل → التقارير

#### أنواع التقارير:

##### أ) تقرير العمال
- إحصائيات العمال النشطين/غير النشطين
- توزيع العمال حسب الجنسية (مع نسب مئوية)
- توزيع العمال حسب نوع العمل (مع نسب مئوية)
- قائمة العمال المضافين حديثاً
- تصدير CSV

##### ب) تقرير الرواتب
- إحصائيات شاملة للرواتب
- تصفية حسب التاريخ/الحالة/العامل
- إحصائيات شهرية مع نسب الإكمال
- تفاصيل جميع المدفوعات
- تصدير CSV

##### ج) التقرير الشهري
- ملخص شامل لشهر محدد
- قائمة المدفوعات للشهر
- تنبيه للعمال بدون راتب
- رابط سريع لإضافة راتب

#### التصدير:
- تصدير بيانات العمال (CSV)
- تصدير بيانات الرواتب (CSV)
- ترميز UTF-8 لدعم العربية

### 5. لوحة التحكم 📈
**الوصول:** الصفحة الرئيسية

#### الإحصائيات السريعة:
- عدد العمال النشطين
- مدفوعات الشهر الحالي
- الرواتب غير المدفوعة
- إجمالي المبلغ المدفوع (ر.ق)

#### المعلومات التفصيلية:
- المدفوعات الأخيرة (5 معاملات)
- إحصائيات الجنسيات (أعلى 5)
- إحصائيات أنواع العمل (أعلى 5)
- إجراءات سريعة

## 🔒 نظام الأمان

### المصادقة:
- تسجيل دخول آمن
- تشفير كلمات المرور
- جلسات محمية
- انتهاء صلاحية تلقائي

### الصلاحيات:
- **مدير النظام:**
  - جميع الصلاحيات
  - إدارة المستخدمين
  - حذف البيانات
  - الوصول للتقارير

- **مستخدم عادي:**
  - إدارة العمال
  - إدارة الرواتب
  - عرض التقارير
  - لا يمكن حذف البيانات

### الحماية:
- منع SQL Injection
- تحقق من صحة البيانات
- منع التكرار
- رسائل خطأ واضحة

## 🇶🇦 التخصيص القطري

### العملة:
- الريال القطري (ر.ق) في جميع أنحاء النظام
- معدل الساعات الإضافية: 20 ر.ق/ساعة

### الوثائق:
- الهوية القطرية (11 رقم)
- بطاقة الإقامة (10 أرقام)
- جواز السفر
- رخصة العمل

### الجنسيات:
- قطر كخيار افتراضي
- تركيز على الجنسيات الشائعة للعمالة المنزلية

### أنواع العمل:
- قسم خاص للأعمال الشائعة في قطر
- تصنيف شامل لجميع أنواع العمالة المنزلية

## 📱 واجهة المستخدم

### التصميم:
- واجهة عربية كاملة مع دعم RTL
- تصميم متجاوب (Bootstrap 5)
- ألوان وأيقونات واضحة
- تجربة مستخدم محسنة

### المميزات:
- رسائل تأكيد ملونة
- مؤشرات التقدم
- نوافذ تأكيد للحذف
- تحميل تلقائي للبيانات
- تحقق فوري من صحة البيانات

## 🔧 الصيانة والنسخ الاحتياطية

### قاعدة البيانات:
- SQLite (qatar_salary_system.db)
- نسخ احتياطية يدوية
- تصدير البيانات CSV

### الملفات المهمة:
- `simple_app.py` - التطبيق الرئيسي
- `qatar_salary_system.db` - قاعدة البيانات
- `templates/` - قوالب الواجهة
- `static/` - الملفات الثابتة

### النسخ الاحتياطية:
1. نسخ ملف قاعدة البيانات
2. تصدير البيانات عبر التقارير
3. نسخ مجلد التطبيق كاملاً

## 📞 الدعم والمساعدة

### الأخطاء الشائعة:
- **خطأ تسجيل الدخول:** تحقق من اسم المستخدم وكلمة المرور
- **خطأ إضافة عامل:** تحقق من رقم الهوية (فريد)
- **خطأ إضافة راتب:** تحقق من عدم وجود راتب للعامل في نفس الشهر

### نصائح الاستخدام:
- استخدم أرقام هوية صحيحة
- احفظ نسخ احتياطية دورية
- راجع التقارير بانتظام
- حدث كلمات المرور دورياً

---

**تم تطوير النظام بعناية ليلبي احتياجات إدارة العمالة المنزلية في دولة قطر 🇶🇦**
